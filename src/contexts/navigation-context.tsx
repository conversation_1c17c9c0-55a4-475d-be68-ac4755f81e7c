'use client';

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import {
  NavigationContextValue,
  SidebarState,
  SidebarActions,
  MainSidebarConfig,
  PageSidebarConfig,
  Permission,
  UserRole,
  NavigationItem,
} from '@/types/navigation';

// Navigation context
const NavigationContext = createContext<NavigationContextValue | null>(null);

// Sidebar state reducer
type SidebarAction =
  | { type: 'TOGGLE' }
  | { type: 'OPEN' }
  | { type: 'CLOSE' }
  | { type: 'COLLAPSE' }
  | { type: 'EXPAND' }
  | { type: 'SET_ACTIVE_ITEM'; payload: string }
  | { type: 'TOGGLE_GROUP'; payload: string }
  | { type: 'PIN_ITEM'; payload: string }
  | { type: 'UNPIN_ITEM'; payload: string }
  | { type: 'LOAD_STATE'; payload: Partial<SidebarState> };

function sidebarReducer(state: SidebarState, action: SidebarAction): SidebarState {
  switch (action.type) {
    case 'TOGGLE':
      return { ...state, isOpen: !state.isOpen };
    case 'OPEN':
      return { ...state, isOpen: true };
    case 'CLOSE':
      return { ...state, isOpen: false };
    case 'COLLAPSE':
      return { ...state, isCollapsed: true };
    case 'EXPAND':
      return { ...state, isCollapsed: false };
    case 'SET_ACTIVE_ITEM':
      return { ...state, activeItem: action.payload };
    case 'TOGGLE_GROUP':
      return {
        ...state,
        expandedGroups: state.expandedGroups.includes(action.payload)
          ? state.expandedGroups.filter(id => id !== action.payload)
          : [...state.expandedGroups, action.payload],
      };
    case 'PIN_ITEM':
      return {
        ...state,
        pinnedItems: state.pinnedItems.includes(action.payload)
          ? state.pinnedItems
          : [...state.pinnedItems, action.payload],
      };
    case 'UNPIN_ITEM':
      return {
        ...state,
        pinnedItems: state.pinnedItems.filter(id => id !== action.payload),
      };
    case 'LOAD_STATE':
      return { ...state, ...action.payload };
    default:
      return state;
  }
}

// Initial sidebar state
const initialSidebarState: SidebarState = {
  isOpen: true,
  isCollapsed: false,
  activeItem: undefined,
  expandedGroups: [],
  pinnedItems: [],
};

// Storage keys
const SIDEBAR_STATE_KEY = 'omnispace-sidebar-state';

interface NavigationProviderProps {
  children: React.ReactNode;
  initialMainSidebar?: Partial<MainSidebarConfig>;
  initialPageSidebars?: Record<string, PageSidebarConfig>;
}

export function NavigationProvider({
  children,
  initialMainSidebar = {},
  initialPageSidebars = {},
}: NavigationProviderProps) {
  const pathname = usePathname();
  const { user, profile } = useAuth();
  
  // Sidebar state management
  const [sidebarState, dispatch] = useReducer(sidebarReducer, initialSidebarState);
  
  // Main sidebar configuration
  const [mainSidebar, setMainSidebar] = React.useState<MainSidebarConfig>({
    id: 'main-sidebar',
    title: 'Omnispace',
    items: [],
    ...initialMainSidebar,
  });
  
  // Page-specific sidebars
  const [pageSidebars, setPageSidebars] = React.useState<Record<string, PageSidebarConfig>>(
    initialPageSidebars
  );

  // Load sidebar state from localStorage on mount
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(SIDEBAR_STATE_KEY);
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        dispatch({ type: 'LOAD_STATE', payload: parsedState });
      }
    } catch (error) {
      console.warn('Failed to load sidebar state from localStorage:', error);
    }
  }, []);

  // Save sidebar state to localStorage when it changes
  useEffect(() => {
    try {
      localStorage.setItem(SIDEBAR_STATE_KEY, JSON.stringify(sidebarState));
    } catch (error) {
      console.warn('Failed to save sidebar state to localStorage:', error);
    }
  }, [sidebarState]);

  // Sidebar actions
  const sidebarActions: SidebarActions = {
    toggle: useCallback(() => dispatch({ type: 'TOGGLE' }), []),
    open: useCallback(() => dispatch({ type: 'OPEN' }), []),
    close: useCallback(() => dispatch({ type: 'CLOSE' }), []),
    collapse: useCallback(() => dispatch({ type: 'COLLAPSE' }), []),
    expand: useCallback(() => dispatch({ type: 'EXPAND' }), []),
    setActiveItem: useCallback((itemId: string) => 
      dispatch({ type: 'SET_ACTIVE_ITEM', payload: itemId }), []),
    toggleGroup: useCallback((groupId: string) => 
      dispatch({ type: 'TOGGLE_GROUP', payload: groupId }), []),
    pinItem: useCallback((itemId: string) => 
      dispatch({ type: 'PIN_ITEM', payload: itemId }), []),
    unpinItem: useCallback((itemId: string) => 
      dispatch({ type: 'UNPIN_ITEM', payload: itemId }), []),
  };

  // Get current page from pathname
  const currentPage = pathname.split('/').filter(Boolean).join('/');

  // Get user role and permissions
  const userRole: UserRole = (profile?.role as UserRole) || 'user';
  const userPermissions: Permission[] = []; // TODO: Implement permission system

  // Permission checking functions
  const checkPermission = useCallback((permission: Permission): boolean => {
    // TODO: Implement actual permission checking logic
    return userPermissions.some(p => 
      p.action === permission.action && p.resource === permission.resource
    );
  }, [userPermissions]);

  const checkRole = useCallback((roles: UserRole[]): boolean => {
    return roles.includes(userRole);
  }, [userRole]);

  // Update functions
  const updateMainSidebar = useCallback((config: Partial<MainSidebarConfig>) => {
    setMainSidebar(prev => ({ ...prev, ...config }));
  }, []);

  const updatePageSidebar = useCallback((pageId: string, config: Partial<PageSidebarConfig>) => {
    setPageSidebars(prev => ({
      ...prev,
      [pageId]: { ...prev[pageId], ...config },
    }));
  }, []);

  // Context value
  const contextValue: NavigationContextValue = {
    mainSidebar,
    pageSidebars,
    currentPage,
    userRole,
    userPermissions,
    sidebarState,
    sidebarActions,
    updateMainSidebar,
    updatePageSidebar,
    checkPermission,
    checkRole,
  };

  return (
    <NavigationContext.Provider value={contextValue}>
      {children}
    </NavigationContext.Provider>
  );
}

// Hook to use navigation context
export function useNavigation(): NavigationContextValue {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
}

// Hook for sidebar state management
export function useSidebarState() {
  const { sidebarState, sidebarActions } = useNavigation();
  return { state: sidebarState, actions: sidebarActions };
}

// Hook for navigation items with permission filtering
export function useNavigationItems(items: NavigationItem[]): NavigationItem[] {
  const { checkPermission, checkRole } = useNavigation();
  
  return React.useMemo(() => {
    return items.filter(item => {
      // Check visibility
      if (item.isVisible === false) return false;
      
      // Check role requirements
      if (item.requiredRoles && !checkRole(item.requiredRoles)) return false;
      
      // Check permission requirements
      if (item.requiredPermissions) {
        return item.requiredPermissions.every(permission => checkPermission(permission));
      }
      
      return true;
    });
  }, [items, checkPermission, checkRole]);
}

// Hook for current page sidebar
export function usePageSidebar(pageId?: string): PageSidebarConfig | undefined {
  const { pageSidebars, currentPage } = useNavigation();
  const targetPageId = pageId || currentPage;
  return pageSidebars[targetPageId];
}

// Hook for responsive sidebar behavior
export function useResponsiveSidebar() {
  const { sidebarState, sidebarActions } = useNavigation();
  const [isMobile, setIsMobile] = React.useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Auto-collapse on mobile
  useEffect(() => {
    if (isMobile && sidebarState.isOpen) {
      sidebarActions.close();
    }
  }, [isMobile, sidebarState.isOpen, sidebarActions]);

  return {
    isMobile,
    shouldShowOverlay: isMobile && sidebarState.isOpen,
    shouldAutoCollapse: isMobile,
  };
}
