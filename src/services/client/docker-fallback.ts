// Client-side fallback for Docker operations when server is not available
import { ContainerInfo, CreateContainerOptions, WorkspaceInfo } from '@/types/docker';

export class DockerFallbackService {
  private isServerAvailable = false;
  
  // Check if Docker service is available
  async checkServerAvailability(): Promise<boolean> {
    try {
      const response = await fetch('/api/docker/ping', {
        method: 'GET',
        credentials: 'include'
      });
      
      this.isServerAvailable = response.ok;
      return this.isServerAvailable;
    } catch (error) {
      console.warn('Docker server check failed:', error);
      this.isServerAvailable = false;
      return false;
    }
  }

  // List containers with fallback
  async listContainers(): Promise<ContainerInfo[]> {
    if (!await this.checkServerAvailability()) {
      return [];
    }

    try {
      const response = await fetch('/api/docker/containers', {
        method: 'GET',
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      return data.success ? data.data : [];
    } catch (error) {
      console.warn('Failed to list containers:', error);
      return [];
    }
  }

  // List workspaces with fallback
  async listWorkspaces(): Promise<WorkspaceInfo[]> {
    if (!await this.checkServerAvailability()) {
      return [];
    }

    try {
      const response = await fetch('/api/workspaces', {
        method: 'GET',
        credentials: 'include'
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required');
        }
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      return data.success ? data.data : [];
    } catch (error) {
      console.warn('Failed to list workspaces:', error);
      if (error instanceof Error && error.message === 'Authentication required') {
        throw error;
      }
      return [];
    }
  }

  // Create container with proper error handling
  async createContainer(options: CreateContainerOptions): Promise<string> {
    if (!await this.checkServerAvailability()) {
      throw new Error('Docker service is not available');
    }

    try {
      const response = await fetch('/api/docker/containers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(options),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Failed to create container');
      }

      return data.data.id;
    } catch (error) {
      console.error('Failed to create container:', error);
      throw error;
    }
  }

  // Get system info with fallback
  async getSystemInfo(): Promise<any> {
    if (!await this.checkServerAvailability()) {
      return {
        connected: false,
        error: 'Docker service not available',
        containers: 0,
        images: 0,
        serverVersion: 'N/A'
      };
    }

    try {
      const response = await fetch('/api/docker/info', {
        method: 'GET',
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      return data.success ? data.data : { connected: false };
    } catch (error) {
      console.warn('Failed to get system info:', error);
      return {
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Start container
  async startContainer(id: string): Promise<void> {
    if (!await this.checkServerAvailability()) {
      throw new Error('Docker service is not available');
    }

    try {
      const response = await fetch(`/api/docker/containers/${id}/start`, {
        method: 'POST',
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Failed to start container');
      }
    } catch (error) {
      console.error('Failed to start container:', error);
      throw error;
    }
  }

  // Stop container
  async stopContainer(id: string, timeout = 10): Promise<void> {
    if (!await this.checkServerAvailability()) {
      throw new Error('Docker service is not available');
    }

    try {
      const response = await fetch(`/api/docker/containers/${id}/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ timeout }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Failed to stop container');
      }
    } catch (error) {
      console.error('Failed to stop container:', error);
      throw error;
    }
  }

  // Restart container
  async restartContainer(id: string): Promise<void> {
    if (!await this.checkServerAvailability()) {
      throw new Error('Docker service is not available');
    }

    try {
      const response = await fetch(`/api/docker/containers/${id}/restart`, {
        method: 'POST',
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Failed to restart container');
      }
    } catch (error) {
      console.error('Failed to restart container:', error);
      throw error;
    }
  }

  // Remove container
  async removeContainer(id: string, force = false): Promise<void> {
    if (!await this.checkServerAvailability()) {
      throw new Error('Docker service is not available');
    }

    try {
      const response = await fetch(`/api/docker/containers/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ force }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Failed to remove container');
      }
    } catch (error) {
      console.error('Failed to remove container:', error);
      throw error;
    }
  }

  // Get container logs
  async getContainerLogs(id: string, tail = 100): Promise<string> {
    if (!await this.checkServerAvailability()) {
      throw new Error('Docker service is not available');
    }

    try {
      const response = await fetch(`/api/docker/containers/${id}/logs?tail=${tail}`, {
        method: 'GET',
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Failed to get container logs');
      }

      return data.data;
    } catch (error) {
      console.error('Failed to get container logs:', error);
      throw error;
    }
  }

  // Get container stats
  async getContainerStats(id: string): Promise<any> {
    if (!await this.checkServerAvailability()) {
      throw new Error('Docker service is not available');
    }

    try {
      const response = await fetch(`/api/docker/containers/${id}/stats`, {
        method: 'GET',
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Failed to get container stats');
      }

      return data.data;
    } catch (error) {
      console.error('Failed to get container stats:', error);
      throw error;
    }
  }

  // Execute command in container
  async execInContainer(id: string, cmd: string[]): Promise<string> {
    if (!await this.checkServerAvailability()) {
      throw new Error('Docker service is not available');
    }

    try {
      const response = await fetch(`/api/docker/containers/${id}/exec`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ cmd }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Failed to execute command');
      }

      return data.data;
    } catch (error) {
      console.error('Failed to execute command in container:', error);
      throw error;
    }
  }

  // Pull image
  async pullImage(imageName: string): Promise<void> {
    if (!await this.checkServerAvailability()) {
      throw new Error('Docker service is not available');
    }

    try {
      const response = await fetch('/api/docker/images/pull', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ imageName }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Failed to pull image');
      }
    } catch (error) {
      console.error('Failed to pull image:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const dockerFallbackService = new DockerFallbackService();