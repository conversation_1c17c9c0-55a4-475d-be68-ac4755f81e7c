import { 
  SSHConnectionConfig, 
  SSHConnectionStatus, 
  RemoteContainerInfo, 
  RemoteDockerImage,
  RemoteDockerCommand,
  RemoteDockerCommandResult
} from '@/types/ssh-docker';

export class SSHDockerAPIService {
  private baseUrl = '/api/ssh-docker';

  /**
   * Get session cookie for authentication
   */
  private getAuthHeaders(): HeadersInit {
    const sessionCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('appwrite-session='))
      ?.split('=')[1];

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (sessionCookie) {
      headers['x-appwrite-session'] = sessionCookie;
    }

    return headers;
  }

  /**
   * Test SSH connection
   */
  async testConnection(config: SSHConnectionConfig): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/test`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        credentials: 'include',
        body: JSON.stringify(config),
      });

      const data = await response.json();
      return response.ok && data.success && data.data.connected;
    } catch (error) {
      console.error('Failed to test SSH connection:', error);
      return false;
    }
  }

  /**
   * Connect to SSH server
   */
  async connect(config: SSHConnectionConfig): Promise<void> {
    const response = await fetch(`${this.baseUrl}/connect`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify(config),
    });

    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to connect to SSH server');
    }
  }

  /**
   * Disconnect from SSH server
   */
  async disconnect(connectionId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/disconnect/${connectionId}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      credentials: 'include',
    });

    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to disconnect from SSH server');
    }
  }

  /**
   * Get connection status
   */
  async getConnectionStatus(connectionId: string): Promise<SSHConnectionStatus | null> {
    try {
      const response = await fetch(`${this.baseUrl}/status/${connectionId}`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
        credentials: 'include',
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        return data.data;
      }
      
      return null;
    } catch (error) {
      console.error('Failed to get connection status:', error);
      return null;
    }
  }

  /**
   * List all connections
   */
  async listConnections(): Promise<SSHConnectionStatus[]> {
    try {
      const response = await fetch(`${this.baseUrl}/connections`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
        credentials: 'include',
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        return data.data;
      }
      
      return [];
    } catch (error) {
      console.error('Failed to list connections:', error);
      return [];
    }
  }

  /**
   * List remote containers
   */
  async listRemoteContainers(connectionId: string): Promise<RemoteContainerInfo[]> {
    try {
      const response = await fetch(`${this.baseUrl}/${connectionId}/containers`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
        credentials: 'include',
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        return data.data;
      }
      
      throw new Error(data.message || 'Failed to list remote containers');
    } catch (error) {
      console.error('Failed to list remote containers:', error);
      throw error;
    }
  }

  /**
   * List remote images
   */
  async listRemoteImages(connectionId: string): Promise<RemoteDockerImage[]> {
    try {
      const response = await fetch(`${this.baseUrl}/${connectionId}/images`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
        credentials: 'include',
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        return data.data;
      }
      
      throw new Error(data.message || 'Failed to list remote images');
    } catch (error) {
      console.error('Failed to list remote images:', error);
      throw error;
    }
  }

  /**
   * Start remote container
   */
  async startRemoteContainer(connectionId: string, containerId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${connectionId}/containers/${containerId}/start`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      credentials: 'include',
    });

    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to start remote container');
    }
  }

  /**
   * Stop remote container
   */
  async stopRemoteContainer(connectionId: string, containerId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${connectionId}/containers/${containerId}/stop`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      credentials: 'include',
    });

    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to stop remote container');
    }
  }

  /**
   * Remove remote container
   */
  async removeRemoteContainer(connectionId: string, containerId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${connectionId}/containers/${containerId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
      credentials: 'include',
    });

    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to remove remote container');
    }
  }

  /**
   * Get remote container logs
   */
  async getRemoteContainerLogs(connectionId: string, containerId: string, tail = 100): Promise<string> {
    const response = await fetch(`${this.baseUrl}/${connectionId}/containers/${containerId}/logs?tail=${tail}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
      credentials: 'include',
    });

    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to get remote container logs');
    }

    return data.data;
  }

  /**
   * Execute remote command
   */
  async execRemoteCommand(connectionId: string, command: RemoteDockerCommand): Promise<RemoteDockerCommandResult> {
    const response = await fetch(`${this.baseUrl}/${connectionId}/exec`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify(command),
    });

    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to execute remote command');
    }

    return data.data;
  }

  /**
   * Pull remote image
   */
  async pullRemoteImage(connectionId: string, imageName: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${connectionId}/images/pull`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify({ imageName }),
    });

    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to pull remote image');
    }
  }

  /**
   * Save SSH connection config
   */
  async saveConnectionConfig(config: SSHConnectionConfig): Promise<void> {
    const response = await fetch(`${this.baseUrl}/config`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify(config),
    });

    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to save connection config');
    }
  }

  /**
   * Get saved connection configs
   */
  async getSavedConnectionConfigs(): Promise<SSHConnectionConfig[]> {
    try {
      const response = await fetch(`${this.baseUrl}/config`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
        credentials: 'include',
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        return data.data;
      }
      
      return [];
    } catch (error) {
      console.error('Failed to get saved connection configs:', error);
      return [];
    }
  }

  /**
   * Delete saved connection config
   */
  async deleteConnectionConfig(connectionId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/config/${connectionId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
      credentials: 'include',
    });

    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to delete connection config');
    }
  }
}

// Export singleton instance
export const sshDockerAPIService = new SSHDockerAPIService();