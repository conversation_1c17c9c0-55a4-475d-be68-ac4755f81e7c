// Import services for internal use
import { AuthService } from './auth';
import { DatabaseService } from './database';
import { StorageService } from './storage';
import { FunctionsService } from './functions';
import { WorkspaceService } from './workspace';
import { WorkspaceFileService } from './workspace-files';
import { WorkspaceTemplateService } from './workspace-templates';

// Export all Appwrite services
export { BaseAppwriteService, connectionPool } from './base';
export { AuthService } from './auth';
export { DatabaseService } from './database';
export { StorageService } from './storage';
export { FunctionsService } from './functions';
export { WorkspaceService } from './workspace';
export { WorkspaceFileService } from './workspace-files';
export { WorkspaceTemplateService } from './workspace-templates';

// Export types
export type {
  ServiceResult,
  PaginationParams,
  PaginatedResult,
  QueryParams
} from './base';

export type {
  CreateUserParams,
  LoginParams,
  UpdateUserParams,
  PasswordResetParams,
  PasswordUpdateParams,
  SessionInfo
} from './auth';

export type {
  DatabaseInfo,
  CollectionInfo,
  AttributeInfo,
  IndexInfo,
  CreateDatabaseParams,
  CreateCollectionParams,
  CreateAttributeParams,
  CreateIndexParams,
  DocumentData,
  CreateDocumentParams,
  UpdateDocumentParams,
  QueryBuilder
} from './database';

export type {
  WorkspaceDocument,
  WorkspacePermissionDocument,
  WorkspaceCollaboratorDocument,
  WorkspaceStatsDocument,
  CreateWorkspaceParams,
  UpdateWorkspaceParams,
  WorkspaceQueryParams
} from './workspace';

export type {
  WorkspaceFileDocument,
  FileVersionDocument,
  CreateFileParams,
  UpdateFileParams,
  FileQueryParams
} from './workspace-files';

export type {
  WorkspaceTemplateDocument,
  TemplateReviewDocument,
  CreateTemplateParams,
  UpdateTemplateParams,
  TemplateQueryParams
} from './workspace-templates';

export type {
  BucketInfo,
  FileInfo,
  CreateBucketParams,
  UpdateBucketParams,
  UploadFileParams,
  FileUploadResult,
  ImageTransformOptions
} from './storage';

export type {
  FunctionInfo,
  DeploymentInfo,
  ExecutionInfo,
  CreateFunctionParams,
  UpdateFunctionParams,
  CreateDeploymentParams,
  ExecuteFunctionParams,
  FunctionVariable
} from './functions';

// Service factory class
export class AppwriteServiceFactory {
  private static authService: AuthService;
  private static databaseService: DatabaseService;
  private static storageService: StorageService;
  private static functionsService: FunctionsService;

  // Get auth service instance
  static getAuthService(): AuthService {
    if (!this.authService) {
      this.authService = new AuthService();
    }
    return this.authService;
  }

  // Get database service instance
  static getDatabaseService(): DatabaseService {
    if (!this.databaseService) {
      this.databaseService = new DatabaseService();
    }
    return this.databaseService;
  }

  // Get storage service instance
  static getStorageService(): StorageService {
    if (!this.storageService) {
      this.storageService = new StorageService();
    }
    return this.storageService;
  }

  // Get functions service instance
  static getFunctionsService(): FunctionsService {
    if (!this.functionsService) {
      this.functionsService = new FunctionsService();
    }
    return this.functionsService;
  }

  // Get all services
  static getAllServices() {
    return {
      auth: this.getAuthService(),
      database: this.getDatabaseService(),
      storage: this.getStorageService(),
      functions: this.getFunctionsService()
    };
  }

  // Health check for all services
  static async healthCheckAll(): Promise<{
    overall: boolean;
    services: {
      auth: boolean;
      database: boolean;
      storage: boolean;
      functions: boolean;
    };
    timestamp: string;
  }> {
    const services = this.getAllServices();
    
    const [authHealth, dbHealth, storageHealth, functionsHealth] = await Promise.all([
      services.auth.healthCheck().catch(() => false),
      services.database.healthCheck().catch(() => false),
      services.storage.healthCheck().catch(() => false),
      services.functions.healthCheck().catch(() => false)
    ]);

    const overall = authHealth && dbHealth && storageHealth && functionsHealth;

    return {
      overall,
      services: {
        auth: authHealth,
        database: dbHealth,
        storage: storageHealth,
        functions: functionsHealth
      },
      timestamp: new Date().toISOString()
    };
  }
}

// Convenience exports for direct service access (lazy initialization)
export const getAuthService = () => AppwriteServiceFactory.getAuthService();
export const getDatabaseService = () => AppwriteServiceFactory.getDatabaseService();
export const getStorageService = () => AppwriteServiceFactory.getStorageService();
export const getFunctionsService = () => AppwriteServiceFactory.getFunctionsService();

// Legacy exports for backward compatibility (lazy initialization)
export const authService = {
  get instance() { return AppwriteServiceFactory.getAuthService(); }
};
export const databaseService = {
  get instance() { return AppwriteServiceFactory.getDatabaseService(); }
};
export const storageService = {
  get instance() { return AppwriteServiceFactory.getStorageService(); }
};
export const functionsService = {
  get instance() { return AppwriteServiceFactory.getFunctionsService(); }
};

// Workspace services (lazy instantiation)
export const getWorkspaceService = () => new WorkspaceService();
export const getWorkspaceFileService = () => new WorkspaceFileService();
export const getWorkspaceTemplateService = () => new WorkspaceTemplateService();

// Export factory
export default AppwriteServiceFactory;
