// Server-only Docker service wrapper to prevent client-side bundling issues
// This wrapper ensures Docker imports only happen server-side

// Runtime check to prevent client-side usage
if (typeof window !== 'undefined') {
  throw new Error(
    'Docker wrapper service cannot be used on the client side. Use the client-side Docker API service instead.'
  );
}

// Type imports only - these won't cause bundling issues
import type { ContainerInfo, WorkspaceInfo, CreateContainerOptions, CreateWorkspaceOptions } from '@/types/docker';

let _dockerService: any = null;

async function getDockerService() {
  if (!_dockerService) {
    // Dynamic import to prevent client-side bundling
    const { dockerService: service } = await import('./docker');
    _dockerService = service;
  }
  return _dockerService;
}

export class DockerServiceWrapper {
  async ping(): Promise<boolean> {
    try {
      const service = await getDockerService();
      return await service.ping();
    } catch (error) {
      console.error('Docker ping failed:', error);
      return false;
    }
  }

  async getSystemInfo() {
    const service = await getDockerService();
    return await service.getSystemInfo();
  }

  async listContainers(all: boolean = true): Promise<ContainerInfo[]> {
    try {
      const service = await getDockerService();
      return await service.listContainers(all);
    } catch (error) {
      console.error('Failed to list containers:', error);
      return [];
    }
  }

  async getContainer(containerId: string): Promise<ContainerInfo | null> {
    try {
      const service = await getDockerService();
      return await service.getContainer(containerId);
    } catch (error) {
      console.error(`Failed to get container ${containerId}:`, error);
      return null;
    }
  }

  async createContainer(options: CreateContainerOptions): Promise<string> {
    const service = await getDockerService();
    return await service.createContainer(options);
  }

  async startContainer(containerId: string): Promise<void> {
    const service = await getDockerService();
    return await service.startContainer(containerId);
  }

  async stopContainer(containerId: string, timeout: number = 10): Promise<void> {
    const service = await getDockerService();
    return await service.stopContainer(containerId, timeout);
  }

  async restartContainer(containerId: string): Promise<void> {
    const service = await getDockerService();
    return await service.restartContainer(containerId);
  }

  async removeContainer(containerId: string, force: boolean = false): Promise<void> {
    const service = await getDockerService();
    return await service.removeContainer(containerId, force);
  }

  async getContainerLogs(containerId: string, tail: number = 100): Promise<string> {
    const service = await getDockerService();
    return await service.getContainerLogs(containerId, tail);
  }

  async getContainerStats(containerId: string): Promise<any> {
    const service = await getDockerService();
    return await service.getContainerStats(containerId);
  }

  async execInContainer(containerId: string, cmd: string[]): Promise<string> {
    const service = await getDockerService();
    return await service.execInContainer(containerId, cmd);
  }

  async pullImage(imageName: string): Promise<void> {
    const service = await getDockerService();
    return await service.pullImage(imageName);
  }

  async listImages(): Promise<any[]> {
    const service = await getDockerService();
    return await service.listImages();
  }

  async executeCommand(
    containerId: string,
    command: string,
    workingDir?: string
  ): Promise<{ exitCode: number; stdout: string; stderr: string }> {
    const service = await getDockerService();
    return await service.executeCommand(containerId, command, workingDir);
  }

  async createWorkspace(options: CreateWorkspaceOptions): Promise<string> {
    const service = await getDockerService();
    return await service.createWorkspace(options);
  }

  async listWorkspaces(): Promise<WorkspaceInfo[]> {
    try {
      const service = await getDockerService();
      
      // First check if Docker is accessible
      const dockerPing = await service.ping();
      if (!dockerPing) {
        console.warn('Docker daemon not accessible');
        return [];
      }
      
      return await service.listWorkspaces();
    } catch (error) {
      console.error('Failed to list workspaces:', error);
      
      // Check if this is a connection/permission error
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (errorMessage.includes('permission denied') || errorMessage.includes('connect:') || errorMessage.includes('ECONNREFUSED')) {
        console.warn('Docker connection issue:', errorMessage);
      }
      
      return [];
    }
  }

  async getWorkspaceInfo(containerId: string): Promise<WorkspaceInfo | null> {
    try {
      const service = await getDockerService();
      return await service.getWorkspaceInfo(containerId);
    } catch (error) {
      console.error(`Failed to get workspace info for ${containerId}:`, error);
      return null;
    }
  }
}

// Export singleton instance
export const dockerService = new DockerServiceWrapper();