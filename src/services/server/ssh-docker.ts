import { Client, ConnectConfig, ClientChannel } from 'ssh2';
import { readFileSync } from 'fs';
import { 
  SSHConnectionConfig, 
  SSHConnectionStatus, 
  RemoteDockerCommand, 
  RemoteDockerCommandResult,
  RemoteContainerInfo,
  RemoteDockerImage
} from '@/types/ssh-docker';

// Runtime check to prevent client-side usage
if (typeof window !== 'undefined') {
  throw new Error(
    'SSH Docker service cannot be used on the client side. Use the client-side API service instead.'
  );
}

export class SSHDockerService {
  private connections: Map<string, Client> = new Map();
  private connectionStatuses: Map<string, SSHConnectionStatus> = new Map();
  private connectionConfigs: Map<string, SSHConnectionConfig> = new Map();

  constructor() {
    // Additional runtime check
    if (typeof window !== 'undefined') {
      throw new Error(
        'SSH Docker service cannot be instantiated on the client side.'
      );
    }
  }

  /**
   * Test SSH connection without storing it
   */
  async testConnection(config: SSHConnectionConfig): Promise<boolean> {
    return new Promise((resolve) => {
      const client = new Client();
      let resolved = false;

      const timeout = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          client.destroy();
          resolve(false);
        }
      }, config.options?.connectTimeout || 10000);

      client.on('ready', async () => {
        if (resolved) return;
        
        try {
          // Test Docker availability
          const dockerAvailable = await this.testDockerAvailability(client);
          clearTimeout(timeout);
          resolved = true;
          client.end();
          resolve(dockerAvailable);
        } catch (error) {
          clearTimeout(timeout);
          resolved = true;
          client.end();
          resolve(false);
        }
      });

      client.on('error', (error) => {
        if (!resolved) {
          console.error('SSH connection test failed:', error);
          clearTimeout(timeout);
          resolved = true;
          resolve(false);
        }
      });

      const connectConfig = this.buildConnectConfig(config);
      client.connect(connectConfig);
    });
  }

  /**
   * Establish SSH connection and store it
   */
  async connect(config: SSHConnectionConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.connections.has(config.id)) {
        this.disconnect(config.id);
      }

      const client = new Client();
      let resolved = false;

      const timeout = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          client.destroy();
          this.updateConnectionStatus(config.id, {
            id: config.id,
            connected: false,
            error: 'Connection timeout'
          });
          reject(new Error('Connection timeout'));
        }
      }, config.options?.connectTimeout || 15000);

      client.on('ready', async () => {
        if (resolved) return;

        try {
          // Test Docker availability
          const dockerInfo = await this.getDockerInfo(client);
          
          clearTimeout(timeout);
          resolved = true;

          this.connections.set(config.id, client);
          this.connectionConfigs.set(config.id, config);
          
          this.updateConnectionStatus(config.id, {
            id: config.id,
            connected: true,
            lastConnected: new Date(),
            dockerAvailable: dockerInfo.available,
            dockerVersion: dockerInfo.version,
            error: undefined
          });

          resolve();
        } catch (error) {
          clearTimeout(timeout);
          resolved = true;
          client.end();
          this.updateConnectionStatus(config.id, {
            id: config.id,
            connected: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          reject(error);
        }
      });

      client.on('error', (error) => {
        if (!resolved) {
          console.error('SSH connection failed:', error);
          clearTimeout(timeout);
          resolved = true;
          this.updateConnectionStatus(config.id, {
            id: config.id,
            connected: false,
            error: error.message
          });
          reject(error);
        }
      });

      client.on('close', () => {
        this.connections.delete(config.id);
        this.updateConnectionStatus(config.id, {
          id: config.id,
          connected: false,
          error: 'Connection closed'
        });
      });

      client.on('end', () => {
        this.connections.delete(config.id);
        this.updateConnectionStatus(config.id, {
          id: config.id,
          connected: false
        });
      });

      const connectConfig = this.buildConnectConfig(config);
      client.connect(connectConfig);
    });
  }

  /**
   * Disconnect SSH connection
   */
  async disconnect(connectionId: string): Promise<void> {
    const client = this.connections.get(connectionId);
    if (client) {
      client.end();
      this.connections.delete(connectionId);
      this.connectionConfigs.delete(connectionId);
    }
    
    this.updateConnectionStatus(connectionId, {
      id: connectionId,
      connected: false
    });
  }

  /**
   * Check if connection is active
   */
  isConnected(connectionId: string): boolean {
    const status = this.connectionStatuses.get(connectionId);
    return status?.connected === true && this.connections.has(connectionId);
  }

  /**
   * Get connection status
   */
  getConnectionStatus(connectionId: string): SSHConnectionStatus | null {
    return this.connectionStatuses.get(connectionId) || null;
  }

  /**
   * List all connections
   */
  getAllConnections(): SSHConnectionStatus[] {
    return Array.from(this.connectionStatuses.values());
  }

  /**
   * Execute remote Docker command
   */
  async execRemoteCommand(connectionId: string, command: RemoteDockerCommand): Promise<RemoteDockerCommandResult> {
    const client = this.connections.get(connectionId);
    if (!client) {
      throw new Error(`No active connection found for ID: ${connectionId}`);
    }

    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const fullCommand = command.args ? `${command.command} ${command.args.join(' ')}` : command.command;
      
      const timeout = setTimeout(() => {
        reject(new Error('Command execution timeout'));
      }, command.timeout || 30000);

      client.exec(fullCommand, { 
        cwd: command.workingDirectory 
      }, (err, stream) => {
        if (err) {
          clearTimeout(timeout);
          reject(err);
          return;
        }

        let stdout = '';
        let stderr = '';
        let exitCode = 0;

        stream.on('close', (code: number) => {
          clearTimeout(timeout);
          const executionTime = Date.now() - startTime;
          
          resolve({
            success: code === 0,
            stdout,
            stderr,
            exitCode: code,
            executionTime
          });
        });

        stream.on('data', (data: Buffer) => {
          stdout += data.toString();
        });

        stream.stderr.on('data', (data: Buffer) => {
          stderr += data.toString();
        });

        stream.on('exit', (code: number) => {
          exitCode = code;
        });
      });
    });
  }

  /**
   * List remote containers
   */
  async listRemoteContainers(connectionId: string): Promise<RemoteContainerInfo[]> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['ps', '-a', '--format', 'json']
    });

    if (!result.success) {
      throw new Error(`Failed to list containers: ${result.stderr}`);
    }

    try {
      const lines = result.stdout.trim().split('\n').filter(line => line.trim());
      const containers: RemoteContainerInfo[] = [];
      const config = this.connectionConfigs.get(connectionId);

      for (const line of lines) {
        if (!line.trim()) continue;
        
        const containerData = JSON.parse(line);
        containers.push({
          id: containerData.ID,
          name: containerData.Names,
          image: containerData.Image,
          status: this.mapDockerStatus(containerData.State),
          created: new Date(containerData.CreatedAt),
          ports: this.parsePortsFromStatus(containerData.Ports),
          remoteHost: config?.host || connectionId
        });
      }

      return containers;
    } catch (error) {
      console.error('Failed to parse container list:', error);
      return [];
    }
  }

  /**
   * List remote images
   */
  async listRemoteImages(connectionId: string): Promise<RemoteDockerImage[]> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['images', '--format', 'json']
    });

    if (!result.success) {
      throw new Error(`Failed to list images: ${result.stderr}`);
    }

    try {
      const lines = result.stdout.trim().split('\n').filter(line => line.trim());
      const images: RemoteDockerImage[] = [];
      const config = this.connectionConfigs.get(connectionId);

      for (const line of lines) {
        if (!line.trim()) continue;
        
        const imageData = JSON.parse(line);
        images.push({
          id: imageData.ID,
          repository: imageData.Repository,
          tag: imageData.Tag,
          size: this.parseSize(imageData.Size),
          created: new Date(imageData.CreatedAt),
          remoteHost: config?.host || connectionId
        });
      }

      return images;
    } catch (error) {
      console.error('Failed to parse image list:', error);
      return [];
    }
  }

  /**
   * Start remote container
   */
  async startRemoteContainer(connectionId: string, containerId: string): Promise<void> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['start', containerId]
    });

    if (!result.success) {
      throw new Error(`Failed to start container: ${result.stderr}`);
    }
  }

  /**
   * Stop remote container
   */
  async stopRemoteContainer(connectionId: string, containerId: string): Promise<void> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['stop', containerId]
    });

    if (!result.success) {
      throw new Error(`Failed to stop container: ${result.stderr}`);
    }
  }

  /**
   * Remove remote container
   */
  async removeRemoteContainer(connectionId: string, containerId: string): Promise<void> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['rm', '-f', containerId]
    });

    if (!result.success) {
      throw new Error(`Failed to remove container: ${result.stderr}`);
    }
  }

  /**
   * Get remote container logs
   */
  async getRemoteContainerLogs(connectionId: string, containerId: string, tail = 100): Promise<string> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['logs', '--tail', tail.toString(), containerId]
    });

    if (!result.success) {
      throw new Error(`Failed to get container logs: ${result.stderr}`);
    }

    return result.stdout;
  }

  /**
   * Pull remote image
   */
  async pullRemoteImage(connectionId: string, imageName: string): Promise<void> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['pull', imageName],
      timeout: 300000 // 5 minutes for image pulls
    });

    if (!result.success) {
      throw new Error(`Failed to pull image: ${result.stderr}`);
    }
  }

  // Private helper methods

  private buildConnectConfig(config: SSHConnectionConfig): ConnectConfig {
    const connectConfig: ConnectConfig = {
      host: config.host,
      port: config.port,
      username: config.username,
      keepaliveInterval: config.options?.keepaliveInterval || 30000,
      keepaliveCountMax: config.options?.keepaliveCountMax || 3,
      readyTimeout: config.options?.readyTimeout || 20000,
    };

    switch (config.authentication.type) {
      case 'password':
        connectConfig.password = config.authentication.password;
        break;
      case 'privateKey':
        if (config.authentication.privateKey) {
          try {
            connectConfig.privateKey = readFileSync(config.authentication.privateKey);
            if (config.authentication.passphrase) {
              connectConfig.passphrase = config.authentication.passphrase;
            }
          } catch (error) {
            throw new Error(`Failed to read private key: ${error}`);
          }
        }
        break;
      case 'agent':
        connectConfig.agent = config.authentication.agentPath || process.env.SSH_AUTH_SOCK;
        break;
    }

    return connectConfig;
  }

  private async testDockerAvailability(client: Client): Promise<boolean> {
    return new Promise((resolve) => {
      client.exec('docker --version', (err, stream) => {
        if (err) {
          resolve(false);
          return;
        }

        let output = '';
        stream.on('data', (data: Buffer) => {
          output += data.toString();
        });

        stream.on('close', (code: number) => {
          resolve(code === 0 && output.includes('Docker'));
        });
      });
    });
  }

  private async getDockerInfo(client: Client): Promise<{ available: boolean; version?: string }> {
    return new Promise((resolve) => {
      client.exec('docker --version', (err, stream) => {
        if (err) {
          resolve({ available: false });
          return;
        }

        let output = '';
        stream.on('data', (data: Buffer) => {
          output += data.toString();
        });

        stream.on('close', (code: number) => {
          if (code === 0 && output.includes('Docker')) {
            const versionMatch = output.match(/Docker version (\S+)/);
            resolve({ 
              available: true, 
              version: versionMatch ? versionMatch[1] : 'unknown' 
            });
          } else {
            resolve({ available: false });
          }
        });
      });
    });
  }

  private updateConnectionStatus(id: string, status: Partial<SSHConnectionStatus>): void {
    const existing = this.connectionStatuses.get(id) || { id, connected: false };
    this.connectionStatuses.set(id, { ...existing, ...status });
  }

  private mapDockerStatus(state: string): RemoteContainerInfo['status'] {
    switch (state.toLowerCase()) {
      case 'running': return 'running';
      case 'exited': return 'stopped';
      case 'paused': return 'paused';
      case 'restarting': return 'restarting';
      default: return 'stopped';
    }
  }

  private parsePortsFromStatus(portsString: string): { [key: string]: string } {
    const ports: { [key: string]: string } = {};
    if (!portsString) return ports;

    // Parse Docker port format: "0.0.0.0:8080->80/tcp, 0.0.0.0:8443->443/tcp"
    const portMappings = portsString.split(', ');
    for (const mapping of portMappings) {
      const match = mapping.match(/(\d+\.\d+\.\d+\.\d+):(\d+)->(\d+)\/(\w+)/);
      if (match) {
        const [, , hostPort, containerPort, protocol] = match;
        ports[`${containerPort}/${protocol}`] = hostPort;
      }
    }

    return ports;
  }

  private parseSize(sizeString: string): number {
    if (!sizeString) return 0;
    
    const units = { 'B': 1, 'KB': 1024, 'MB': 1024**2, 'GB': 1024**3 };
    const match = sizeString.match(/^([\d.]+)\s*([KMGT]?B)$/i);
    
    if (match) {
      const [, size, unit] = match;
      return parseFloat(size) * (units[unit.toUpperCase() as keyof typeof units] || 1);
    }
    
    return 0;
  }
}

// Export singleton instance
export const sshDockerService = new SSHDockerService();import { Client, ConnectConfig, ClientChannel } from 'ssh2';
import { readFileSync } from 'fs';
import { 
  SSHConnectionConfig, 
  SSHConnectionStatus, 
  RemoteDockerCommand, 
  RemoteDockerCommandResult,
  RemoteContainerInfo,
  RemoteDockerImage
} from '@/types/ssh-docker';

// Runtime check to prevent client-side usage
if (typeof window !== 'undefined') {
  throw new Error(
    'SSH Docker service cannot be used on the client side. Use the client-side API service instead.'
  );
}

export class SSHDockerService {
  private connections: Map<string, Client> = new Map();
  private connectionStatuses: Map<string, SSHConnectionStatus> = new Map();
  private connectionConfigs: Map<string, SSHConnectionConfig> = new Map();

  constructor() {
    // Additional runtime check
    if (typeof window !== 'undefined') {
      throw new Error(
        'SSH Docker service cannot be instantiated on the client side.'
      );
    }
  }

  /**
   * Test SSH connection without storing it
   */
  async testConnection(config: SSHConnectionConfig): Promise<boolean> {
    return new Promise((resolve) => {
      const client = new Client();
      let resolved = false;

      const timeout = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          client.destroy();
          resolve(false);
        }
      }, config.options?.connectTimeout || 10000);

      client.on('ready', async () => {
        if (resolved) return;
        
        try {
          // Test Docker availability
          const dockerAvailable = await this.testDockerAvailability(client);
          clearTimeout(timeout);
          resolved = true;
          client.end();
          resolve(dockerAvailable);
        } catch (error) {
          clearTimeout(timeout);
          resolved = true;
          client.end();
          resolve(false);
        }
      });

      client.on('error', (error) => {
        if (!resolved) {
          console.error('SSH connection test failed:', error);
          clearTimeout(timeout);
          resolved = true;
          resolve(false);
        }
      });

      const connectConfig = this.buildConnectConfig(config);
      client.connect(connectConfig);
    });
  }

  /**
   * Establish SSH connection and store it
   */
  async connect(config: SSHConnectionConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.connections.has(config.id)) {
        this.disconnect(config.id);
      }

      const client = new Client();
      let resolved = false;

      const timeout = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          client.destroy();
          this.updateConnectionStatus(config.id, {
            id: config.id,
            connected: false,
            error: 'Connection timeout'
          });
          reject(new Error('Connection timeout'));
        }
      }, config.options?.connectTimeout || 15000);

      client.on('ready', async () => {
        if (resolved) return;

        try {
          // Test Docker availability
          const dockerInfo = await this.getDockerInfo(client);
          
          clearTimeout(timeout);
          resolved = true;

          this.connections.set(config.id, client);
          this.connectionConfigs.set(config.id, config);
          
          this.updateConnectionStatus(config.id, {
            id: config.id,
            connected: true,
            lastConnected: new Date(),
            dockerAvailable: dockerInfo.available,
            dockerVersion: dockerInfo.version,
            error: undefined
          });

          resolve();
        } catch (error) {
          clearTimeout(timeout);
          resolved = true;
          client.end();
          this.updateConnectionStatus(config.id, {
            id: config.id,
            connected: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          reject(error);
        }
      });

      client.on('error', (error) => {
        if (!resolved) {
          console.error('SSH connection failed:', error);
          clearTimeout(timeout);
          resolved = true;
          this.updateConnectionStatus(config.id, {
            id: config.id,
            connected: false,
            error: error.message
          });
          reject(error);
        }
      });

      client.on('close', () => {
        this.connections.delete(config.id);
        this.updateConnectionStatus(config.id, {
          id: config.id,
          connected: false,
          error: 'Connection closed'
        });
      });

      client.on('end', () => {
        this.connections.delete(config.id);
        this.updateConnectionStatus(config.id, {
          id: config.id,
          connected: false
        });
      });

      const connectConfig = this.buildConnectConfig(config);
      client.connect(connectConfig);
    });
  }

  /**
   * Disconnect SSH connection
   */
  async disconnect(connectionId: string): Promise<void> {
    const client = this.connections.get(connectionId);
    if (client) {
      client.end();
      this.connections.delete(connectionId);
      this.connectionConfigs.delete(connectionId);
    }
    
    this.updateConnectionStatus(connectionId, {
      id: connectionId,
      connected: false
    });
  }

  /**
   * Check if connection is active
   */
  isConnected(connectionId: string): boolean {
    const status = this.connectionStatuses.get(connectionId);
    return status?.connected === true && this.connections.has(connectionId);
  }

  /**
   * Get connection status
   */
  getConnectionStatus(connectionId: string): SSHConnectionStatus | null {
    return this.connectionStatuses.get(connectionId) || null;
  }

  /**
   * List all connections
   */
  getAllConnections(): SSHConnectionStatus[] {
    return Array.from(this.connectionStatuses.values());
  }

  /**
   * Execute remote Docker command
   */
  async execRemoteCommand(connectionId: string, command: RemoteDockerCommand): Promise<RemoteDockerCommandResult> {
    const client = this.connections.get(connectionId);
    if (!client) {
      throw new Error(`No active connection found for ID: ${connectionId}`);
    }

    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const fullCommand = command.args ? `${command.command} ${command.args.join(' ')}` : command.command;
      
      const timeout = setTimeout(() => {
        reject(new Error('Command execution timeout'));
      }, command.timeout || 30000);

      client.exec(fullCommand, { 
        cwd: command.workingDirectory 
      }, (err, stream) => {
        if (err) {
          clearTimeout(timeout);
          reject(err);
          return;
        }

        let stdout = '';
        let stderr = '';
        let exitCode = 0;

        stream.on('close', (code: number) => {
          clearTimeout(timeout);
          const executionTime = Date.now() - startTime;
          
          resolve({
            success: code === 0,
            stdout,
            stderr,
            exitCode: code,
            executionTime
          });
        });

        stream.on('data', (data: Buffer) => {
          stdout += data.toString();
        });

        stream.stderr.on('data', (data: Buffer) => {
          stderr += data.toString();
        });

        stream.on('exit', (code: number) => {
          exitCode = code;
        });
      });
    });
  }

  /**
   * List remote containers
   */
  async listRemoteContainers(connectionId: string): Promise<RemoteContainerInfo[]> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['ps', '-a', '--format', 'json']
    });

    if (!result.success) {
      throw new Error(`Failed to list containers: ${result.stderr}`);
    }

    try {
      const lines = result.stdout.trim().split('\n').filter(line => line.trim());
      const containers: RemoteContainerInfo[] = [];
      const config = this.connectionConfigs.get(connectionId);

      for (const line of lines) {
        if (!line.trim()) continue;
        
        const containerData = JSON.parse(line);
        containers.push({
          id: containerData.ID,
          name: containerData.Names,
          image: containerData.Image,
          status: this.mapDockerStatus(containerData.State),
          created: new Date(containerData.CreatedAt),
          ports: this.parsePortsFromStatus(containerData.Ports),
          remoteHost: config?.host || connectionId
        });
      }

      return containers;
    } catch (error) {
      console.error('Failed to parse container list:', error);
      return [];
    }
  }

  /**
   * List remote images
   */
  async listRemoteImages(connectionId: string): Promise<RemoteDockerImage[]> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['images', '--format', 'json']
    });

    if (!result.success) {
      throw new Error(`Failed to list images: ${result.stderr}`);
    }

    try {
      const lines = result.stdout.trim().split('\n').filter(line => line.trim());
      const images: RemoteDockerImage[] = [];
      const config = this.connectionConfigs.get(connectionId);

      for (const line of lines) {
        if (!line.trim()) continue;
        
        const imageData = JSON.parse(line);
        images.push({
          id: imageData.ID,
          repository: imageData.Repository,
          tag: imageData.Tag,
          size: this.parseSize(imageData.Size),
          created: new Date(imageData.CreatedAt),
          remoteHost: config?.host || connectionId
        });
      }

      return images;
    } catch (error) {
      console.error('Failed to parse image list:', error);
      return [];
    }
  }

  /**
   * Start remote container
   */
  async startRemoteContainer(connectionId: string, containerId: string): Promise<void> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['start', containerId]
    });

    if (!result.success) {
      throw new Error(`Failed to start container: ${result.stderr}`);
    }
  }

  /**
   * Stop remote container
   */
  async stopRemoteContainer(connectionId: string, containerId: string): Promise<void> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['stop', containerId]
    });

    if (!result.success) {
      throw new Error(`Failed to stop container: ${result.stderr}`);
    }
  }

  /**
   * Remove remote container
   */
  async removeRemoteContainer(connectionId: string, containerId: string): Promise<void> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['rm', '-f', containerId]
    });

    if (!result.success) {
      throw new Error(`Failed to remove container: ${result.stderr}`);
    }
  }

  /**
   * Get remote container logs
   */
  async getRemoteContainerLogs(connectionId: string, containerId: string, tail = 100): Promise<string> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['logs', '--tail', tail.toString(), containerId]
    });

    if (!result.success) {
      throw new Error(`Failed to get container logs: ${result.stderr}`);
    }

    return result.stdout;
  }

  /**
   * Pull remote image
   */
  async pullRemoteImage(connectionId: string, imageName: string): Promise<void> {
    const result = await this.execRemoteCommand(connectionId, {
      command: 'docker',
      args: ['pull', imageName],
      timeout: 300000 // 5 minutes for image pulls
    });

    if (!result.success) {
      throw new Error(`Failed to pull image: ${result.stderr}`);
    }
  }

  // Private helper methods

  private buildConnectConfig(config: SSHConnectionConfig): ConnectConfig {
    const connectConfig: ConnectConfig = {
      host: config.host,
      port: config.port,
      username: config.username,
      keepaliveInterval: config.options?.keepaliveInterval || 30000,
      keepaliveCountMax: config.options?.keepaliveCountMax || 3,
      readyTimeout: config.options?.readyTimeout || 20000,
    };

    switch (config.authentication.type) {
      case 'password':
        connectConfig.password = config.authentication.password;
        break;
      case 'privateKey':
        if (config.authentication.privateKey) {
          try {
            connectConfig.privateKey = readFileSync(config.authentication.privateKey);
            if (config.authentication.passphrase) {
              connectConfig.passphrase = config.authentication.passphrase;
            }
          } catch (error) {
            throw new Error(`Failed to read private key: ${error}`);
          }
        }
        break;
      case 'agent':
        connectConfig.agent = config.authentication.agentPath || process.env.SSH_AUTH_SOCK;
        break;
    }

    return connectConfig;
  }

  private async testDockerAvailability(client: Client): Promise<boolean> {
    return new Promise((resolve) => {
      client.exec('docker --version', (err, stream) => {
        if (err) {
          resolve(false);
          return;
        }

        let output = '';
        stream.on('data', (data: Buffer) => {
          output += data.toString();
        });

        stream.on('close', (code: number) => {
          resolve(code === 0 && output.includes('Docker'));
        });
      });
    });
  }

  private async getDockerInfo(client: Client): Promise<{ available: boolean; version?: string }> {
    return new Promise((resolve) => {
      client.exec('docker --version', (err, stream) => {
        if (err) {
          resolve({ available: false });
          return;
        }

        let output = '';
        stream.on('data', (data: Buffer) => {
          output += data.toString();
        });

        stream.on('close', (code: number) => {
          if (code === 0 && output.includes('Docker')) {
            const versionMatch = output.match(/Docker version (\S+)/);
            resolve({ 
              available: true, 
              version: versionMatch ? versionMatch[1] : 'unknown' 
            });
          } else {
            resolve({ available: false });
          }
        });
      });
    });
  }

  private updateConnectionStatus(id: string, status: Partial<SSHConnectionStatus>): void {
    const existing = this.connectionStatuses.get(id) || { id, connected: false };
    this.connectionStatuses.set(id, { ...existing, ...status });
  }

  private mapDockerStatus(state: string): RemoteContainerInfo['status'] {
    switch (state.toLowerCase()) {
      case 'running': return 'running';
      case 'exited': return 'stopped';
      case 'paused': return 'paused';
      case 'restarting': return 'restarting';
      default: return 'stopped';
    }
  }

  private parsePortsFromStatus(portsString: string): { [key: string]: string } {
    const ports: { [key: string]: string } = {};
    if (!portsString) return ports;

    // Parse Docker port format: "0.0.0.0:8080->80/tcp, 0.0.0.0:8443->443/tcp"
    const portMappings = portsString.split(', ');
    for (const mapping of portMappings) {
      const match = mapping.match(/(\d+\.\d+\.\d+\.\d+):(\d+)->(\d+)\/(\w+)/);
      if (match) {
        const [, , hostPort, containerPort, protocol] = match;
        ports[`${containerPort}/${protocol}`] = hostPort;
      }
    }

    return ports;
  }

  private parseSize(sizeString: string): number {
    if (!sizeString) return 0;
    
    const units = { 'B': 1, 'KB': 1024, 'MB': 1024**2, 'GB': 1024**3 };
    const match = sizeString.match(/^([\d.]+)\s*([KMGT]?B)$/i);
    
    if (match) {
      const [, size, unit] = match;
      return parseFloat(size) * (units[unit.toUpperCase() as keyof typeof units] || 1);
    }
    
    return 0;
  }
}

// Export singleton instance
export const sshDockerService = new SSHDockerService();