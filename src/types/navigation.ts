/**
 * Navigation Types and Interfaces
 * Comprehensive TypeScript types for navigation components and page sidebars
 */

import { ReactNode } from 'react';
import { LucideIcon } from 'lucide-react';

// Base navigation types
export interface NavigationItem {
  id: string;
  title: string;
  url?: string;
  icon?: LucideIcon;
  isActive?: boolean;
  badge?: string | number;
  children?: NavigationItem[];
  onClick?: () => void;
  disabled?: boolean;
  hidden?: boolean;
}

// Quick action types
export interface QuickAction {
  id: string;
  type: 'button' | 'link' | 'dropdown';
  title: string;
  icon?: LucideIcon;
  onClick?: () => void;
  href?: string;
  badge?: string | number;
  variant?: 'default' | 'primary' | 'secondary' | 'destructive' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  hidden?: boolean;
  children?: QuickAction[];
}

// Sidebar configuration
export interface SidebarConfig {
  title: string;
  description?: string;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  width?: number;
  minWidth?: number;
  maxWidth?: number;
  position?: 'left' | 'right';
  variant?: 'default' | 'glass' | 'floating';
  showHeader?: boolean;
  showFooter?: boolean;
}

// Page sidebar props interface
export interface PageSidebarProps {
  config?: SidebarConfig;
  onQuickAction?: (action: QuickAction) => void;
  onNavigate?: (item: NavigationItem) => void;
  className?: string;
  children?: ReactNode;
  animate?: boolean;
  loading?: boolean;
  error?: string;
}

// Stats and metrics types
export interface StatItem {
  id: string;
  label: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease' | 'neutral';
    period?: string;
  };
  icon?: LucideIcon;
  color?: 'default' | 'primary' | 'success' | 'warning' | 'danger';
  format?: 'number' | 'percentage' | 'currency' | 'bytes' | 'duration';
}

// Activity feed types
export interface ActivityItem {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  description?: string;
  timestamp: Date;
  icon?: LucideIcon;
  user?: {
    name: string;
    avatar?: string;
  };
  metadata?: Record<string, any>;
}

// Filter types
export interface FilterOption {
  id: string;
  label: string;
  value: string;
  count?: number;
  icon?: LucideIcon;
  color?: string;
}

export interface FilterGroup {
  id: string;
  label: string;
  type: 'select' | 'multiselect' | 'radio' | 'checkbox' | 'range' | 'date';
  options: FilterOption[];
  defaultValue?: string | string[];
  placeholder?: string;
}

// System status types
export interface SystemStatus {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'degraded' | 'maintenance';
  uptime?: number;
  lastCheck?: Date;
  description?: string;
  metrics?: {
    cpu?: number;
    memory?: number;
    disk?: number;
    network?: number;
  };
}

// User permission types
export type Permission = 'read' | 'write' | 'admin' | 'owner';
export type UserRole = 'viewer' | 'editor' | 'admin' | 'owner';

export interface UserPermissions {
  role: UserRole;
  permissions: Permission[];
  canCreate?: boolean;
  canEdit?: boolean;
  canDelete?: boolean;
  canManageUsers?: boolean;
  canManageSettings?: boolean;
  canViewBilling?: boolean;
  canManageBilling?: boolean;
}

// Responsive behavior types
export interface ResponsiveConfig {
  mobile?: {
    hidden?: boolean;
    collapsed?: boolean;
    overlay?: boolean;
  };
  tablet?: {
    hidden?: boolean;
    collapsed?: boolean;
    width?: number;
  };
  desktop?: {
    width?: number;
    minWidth?: number;
    maxWidth?: number;
  };
}

// Theme and styling types
export interface SidebarTheme {
  variant?: 'default' | 'glass' | 'solid' | 'gradient';
  colorScheme?: 'light' | 'dark' | 'auto';
  accentColor?: string;
  borderRadius?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  blur?: boolean;
  opacity?: number;
}

// Animation types
export interface AnimationConfig {
  enabled?: boolean;
  duration?: number;
  easing?: string;
  stagger?: number;
  entrance?: 'fade' | 'slide' | 'scale' | 'bounce';
  exit?: 'fade' | 'slide' | 'scale' | 'bounce';
}

// Search types
export interface SearchConfig {
  enabled?: boolean;
  placeholder?: string;
  debounceMs?: number;
  minLength?: number;
  searchFields?: string[];
  caseSensitive?: boolean;
}

// Notification types
export interface NotificationItem {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message?: string;
  timestamp: Date;
  read?: boolean;
  actions?: QuickAction[];
  persistent?: boolean;
  autoHide?: boolean;
  hideAfter?: number;
}

// Export utility types
export type SidebarVariant = 'default' | 'glass' | 'floating' | 'minimal';
export type SidebarPosition = 'left' | 'right';
export type SidebarSize = 'sm' | 'md' | 'lg' | 'xl';
export type StatusType = 'online' | 'offline' | 'degraded' | 'maintenance' | 'unknown';
export type ActivityType = 'info' | 'success' | 'warning' | 'error';
export type ChangeType = 'increase' | 'decrease' | 'neutral';

// Component-specific interfaces
export interface DashboardSidebarProps extends PageSidebarProps {
  stats?: StatItem[];
  activities?: ActivityItem[];
  systemStatus?: SystemStatus[];
  onRefreshStats?: () => void;
  onViewActivity?: (activity: ActivityItem) => void;
}

export interface ImagesSidebarProps extends PageSidebarProps {
  onPullImage?: (imageName: string) => void;
  onBuildImage?: () => void;
  onManageRegistry?: () => void;
  onViewStorage?: () => void;
}

export interface SettingsSidebarProps extends PageSidebarProps {
  currentSection?: string;
  onSectionChange?: (section: string) => void;
  userPermissions?: UserPermissions;
}
