/**
 * Navigation Types and Interfaces
 * Comprehensive type definitions for sidebar navigation, role-based access control,
 * and page-specific navigation structures
 */

import { LucideIcon } from 'lucide-react';
import { ReactNode } from 'react';

// User roles and permissions
export type UserRole = 'admin' | 'user' | 'moderator' | 'viewer' | 'developer';

export interface Permission {
  action: string;
  resource: string;
  conditions?: Record<string, any>;
}

export interface RolePermissions {
  role: UserRole;
  permissions: Permission[];
  inherits?: UserRole[];
}

// Navigation item types
export type NavigationItemType = 'link' | 'button' | 'dropdown' | 'separator' | 'group';

export interface BaseNavigationItem {
  id: string;
  type: NavigationItemType;
  title: string;
  description?: string;
  icon?: LucideIcon;
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
    color?: string;
  };
  isVisible?: boolean;
  isDisabled?: boolean;
  requiredRoles?: UserRole[];
  requiredPermissions?: Permission[];
  order?: number;
  className?: string;
}

export interface NavigationLink extends BaseNavigationItem {
  type: 'link';
  href: string;
  isExternal?: boolean;
  target?: '_blank' | '_self' | '_parent' | '_top';
  prefetch?: boolean;
}

export interface NavigationButton extends BaseNavigationItem {
  type: 'button';
  onClick: () => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

export interface NavigationDropdown extends BaseNavigationItem {
  type: 'dropdown';
  items: NavigationItem[];
  trigger?: ReactNode;
  align?: 'start' | 'center' | 'end';
  side?: 'top' | 'right' | 'bottom' | 'left';
}

export interface NavigationSeparator extends Omit<BaseNavigationItem, 'title'> {
  type: 'separator';
  title?: never;
}

export interface NavigationGroup extends BaseNavigationItem {
  type: 'group';
  items: NavigationItem[];
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

export type NavigationItem = 
  | NavigationLink 
  | NavigationButton 
  | NavigationDropdown 
  | NavigationSeparator 
  | NavigationGroup;

// Sidebar configuration
export interface SidebarConfig {
  id: string;
  title: string;
  description?: string;
  width?: string;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  position?: 'left' | 'right';
  variant?: 'default' | 'floating' | 'inset';
  className?: string;
  items: NavigationItem[];
}

// Page-specific sidebar configurations
export interface PageSidebarConfig extends SidebarConfig {
  pageId: string;
  pagePath: string;
  quickActions?: NavigationItem[];
  contextualInfo?: {
    title: string;
    content: ReactNode;
  }[];
  filters?: {
    id: string;
    label: string;
    type: 'select' | 'checkbox' | 'radio' | 'search';
    options?: { label: string; value: string }[];
    defaultValue?: any;
    onChange: (value: any) => void;
  }[];
}

// Main dashboard sidebar configuration
export interface MainSidebarConfig extends SidebarConfig {
  user?: {
    name: string;
    email: string;
    avatar?: string;
    role: UserRole;
  };
  branding?: {
    logo: ReactNode;
    title: string;
    subtitle?: string;
  };
  footer?: NavigationItem[];
}

// Sidebar state management
export interface SidebarState {
  isOpen: boolean;
  isCollapsed: boolean;
  activeItem?: string;
  expandedGroups: string[];
  pinnedItems: string[];
}

export interface SidebarActions {
  toggle: () => void;
  open: () => void;
  close: () => void;
  collapse: () => void;
  expand: () => void;
  setActiveItem: (itemId: string) => void;
  toggleGroup: (groupId: string) => void;
  pinItem: (itemId: string) => void;
  unpinItem: (itemId: string) => void;
}

// Navigation context
export interface NavigationContextValue {
  mainSidebar: MainSidebarConfig;
  pageSidebars: Record<string, PageSidebarConfig>;
  currentPage: string;
  userRole: UserRole;
  userPermissions: Permission[];
  sidebarState: SidebarState;
  sidebarActions: SidebarActions;
  updateMainSidebar: (config: Partial<MainSidebarConfig>) => void;
  updatePageSidebar: (pageId: string, config: Partial<PageSidebarConfig>) => void;
  checkPermission: (permission: Permission) => boolean;
  checkRole: (roles: UserRole[]) => boolean;
}

// Breadcrumb types
export interface BreadcrumbItem {
  id: string;
  label: string;
  href?: string;
  icon?: LucideIcon;
  isLast?: boolean;
  isClickable?: boolean;
  dropdown?: {
    items: NavigationItem[];
  };
}

export interface BreadcrumbConfig {
  items: BreadcrumbItem[];
  separator?: ReactNode;
  maxItems?: number;
  showHome?: boolean;
  homeHref?: string;
  className?: string;
}

// Animation and theme types
export interface SidebarAnimationConfig {
  enabled: boolean;
  duration: number;
  easing: string;
  stagger: number;
  hover: {
    scale: number;
    duration: number;
  };
  collapse: {
    duration: number;
    easing: string;
  };
}

export interface SidebarThemeConfig {
  glassmorphism: {
    enabled: boolean;
    opacity: number;
    blur: number;
  };
  colors: {
    background: string;
    foreground: string;
    accent: string;
    border: string;
    hover: string;
    active: string;
  };
  spacing: {
    padding: string;
    margin: string;
    gap: string;
  };
  typography: {
    fontSize: string;
    fontWeight: string;
    lineHeight: string;
  };
}

// Utility types
export type NavigationItemFilter = (item: NavigationItem, context: NavigationContextValue) => boolean;
export type NavigationItemTransformer = (item: NavigationItem, context: NavigationContextValue) => NavigationItem;

export interface NavigationUtils {
  filterItems: (items: NavigationItem[], filter: NavigationItemFilter) => NavigationItem[];
  transformItems: (items: NavigationItem[], transformer: NavigationItemTransformer) => NavigationItem[];
  findItem: (items: NavigationItem[], predicate: (item: NavigationItem) => boolean) => NavigationItem | undefined;
  flattenItems: (items: NavigationItem[]) => NavigationItem[];
  generateBreadcrumbs: (path: string, config?: Partial<BreadcrumbConfig>) => BreadcrumbItem[];
}

// Dashboard page configurations
export interface DashboardPageConfig {
  id: string;
  title: string;
  path: string;
  icon: LucideIcon;
  description?: string;
  requiredRoles?: UserRole[];
  sidebar?: PageSidebarConfig;
  breadcrumbs?: BreadcrumbItem[];
  quickActions?: NavigationItem[];
  metadata?: {
    keywords: string[];
    category: string;
    priority: number;
  };
}

// Predefined dashboard pages
export type DashboardPageId =
  | 'overview'
  | 'workspaces'
  | 'workspaces-create'
  | 'workspaces-overview'
  | 'monitoring'
  | 'containers'
  | 'images'
  | 'settings'
  | 'settings-profile'
  | 'settings-security'
  | 'settings-billing'
  | 'settings-integrations';

// Sidebar component props
export interface SidebarComponentProps {
  config: SidebarConfig;
  state: SidebarState;
  actions: SidebarActions;
  className?: string;
  children?: ReactNode;
}

export interface MainSidebarProps extends SidebarComponentProps {
  config: MainSidebarConfig;
  onNavigate?: (item: NavigationItem) => void;
}

export interface PageSidebarProps extends SidebarComponentProps {
  config: PageSidebarConfig;
  onQuickAction?: (action: NavigationItem) => void;
  onFilterChange?: (filterId: string, value: any) => void;
}

// Navigation events
export interface NavigationEvent {
  type: 'navigate' | 'action' | 'filter' | 'state-change';
  item?: NavigationItem;
  data?: any;
  timestamp: number;
}

export type NavigationEventHandler = (event: NavigationEvent) => void;

// Search and filtering
export interface NavigationSearchConfig {
  enabled: boolean;
  placeholder: string;
  searchFields: string[];
  minLength: number;
  debounceMs: number;
  caseSensitive: boolean;
  highlightMatches: boolean;
}

export interface NavigationFilterConfig {
  id: string;
  label: string;
  type: 'text' | 'select' | 'multiselect' | 'date' | 'range';
  options?: { label: string; value: any }[];
  defaultValue?: any;
  validation?: (value: any) => boolean | string;
}

// Responsive behavior
export interface ResponsiveConfig {
  breakpoints: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  behavior: {
    mobile: 'overlay' | 'push' | 'hidden';
    tablet: 'overlay' | 'push' | 'collapsed';
    desktop: 'expanded' | 'collapsed';
  };
  autoCollapse: boolean;
  persistState: boolean;
}

// Keyboard navigation
export interface KeyboardNavigationConfig {
  enabled: boolean;
  shortcuts: Record<string, string>;
  focusManagement: boolean;
  skipLinks: boolean;
  announcements: boolean;
}

// All types are already exported above with their declarations
