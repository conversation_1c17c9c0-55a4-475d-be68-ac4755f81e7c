export interface SSHConnectionConfig {
  id: string;
  name: string;
  host: string;
  port: number;
  username: string;
  authentication: {
    type: 'password' | 'privateKey' | 'agent';
    password?: string;
    privateKey?: string;
    passphrase?: string;
    agentPath?: string;
  };
  dockerConfig?: {
    socketPath?: string;
    host?: string;
    port?: number;
    tlsEnabled?: boolean;
    certPath?: string;
    keyPath?: string;
    caPath?: string;
  };
  options?: {
    keepaliveInterval?: number;
    keepaliveCountMax?: number;
    readyTimeout?: number;
    connectTimeout?: number;
  };
}

export interface SSHConnectionStatus {
  id: string;
  connected: boolean;
  lastConnected?: Date;
  error?: string;
  dockerAvailable?: boolean;
  dockerVersion?: string;
  serverInfo?: {
    hostname?: string;
    platform?: string;
    arch?: string;
    uptime?: string;
  };
}

export interface RemoteDockerCommand {
  command: string;
  args?: string[];
  workingDirectory?: string;
  timeout?: number;
}

export interface RemoteDockerCommandResult {
  success: boolean;
  stdout: string;
  stderr: string;
  exitCode: number;
  executionTime: number;
}

export interface RemoteContainerInfo {
  id: string;
  name: string;
  image: string;
  status: 'running' | 'stopped' | 'starting' | 'stopping' | 'paused' | 'restarting';
  created: Date;
  ports: { [key: string]: string };
  cpu?: number;
  memory?: number;
  networkMode?: string;
  mountedVolumes?: string[];
  remoteHost: string; // Which SSH connection this container belongs to
}

export interface RemoteDockerImage {
  id: string;
  repository: string;
  tag: string;
  size: number;
  created: Date;
  remoteHost: string;
}

export interface SSHDockerService {
  // Connection management
  connect(config: SSHConnectionConfig): Promise<void>;
  disconnect(connectionId: string): Promise<void>;
  isConnected(connectionId: string): boolean;
  getConnectionStatus(connectionId: string): SSHConnectionStatus | null;
  testConnection(config: SSHConnectionConfig): Promise<boolean>;

  // Docker operations via SSH
  listRemoteContainers(connectionId: string): Promise<RemoteContainerInfo[]>;
  listRemoteImages(connectionId: string): Promise<RemoteDockerImage[]>;
  createRemoteContainer(connectionId: string, options: any): Promise<string>;
  startRemoteContainer(connectionId: string, containerId: string): Promise<void>;
  stopRemoteContainer(connectionId: string, containerId: string): Promise<void>;
  removeRemoteContainer(connectionId: string, containerId: string): Promise<void>;
  getRemoteContainerLogs(connectionId: string, containerId: string, tail?: number): Promise<string>;
  execRemoteCommand(connectionId: string, command: RemoteDockerCommand): Promise<RemoteDockerCommandResult>;

  // File operations via SFTP
  uploadFile(connectionId: string, localPath: string, remotePath: string): Promise<void>;
  downloadFile(connectionId: string, remotePath: string, localPath: string): Promise<void>;
  listRemoteDirectory(connectionId: string, remotePath: string): Promise<any[]>;
}