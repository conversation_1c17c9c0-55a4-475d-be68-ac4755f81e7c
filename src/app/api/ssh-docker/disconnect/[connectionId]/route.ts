import { NextRequest, NextResponse } from 'next/server';
import { sshDockerService } from '@/services/server/ssh-docker';

interface Params {
  connectionId: string;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const { connectionId } = params;

    if (!connectionId) {
      return NextResponse.json(
        { success: false, message: 'Connection ID is required' },
        { status: 400 }
      );
    }

    // Disconnect from the SSH server
    await sshDockerService.disconnect(connectionId);

    return NextResponse.json({
      success: true,
      message: 'Disconnected successfully'
    });
  } catch (error) {
    console.error('SSH disconnection failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Disconnection failed' 
      },
      { status: 500 }
    );
  }
}