import { NextRequest, NextResponse } from 'next/server';
import { sshDockerService } from '@/services/server/ssh-docker';
import { SSHConnectionConfig } from '@/types/ssh-docker';

export async function POST(request: NextRequest) {
  try {
    const config: SSHConnectionConfig = await request.json();

    // Validate required fields
    if (!config.host || !config.username || !config.authentication) {
      return NextResponse.json(
        { success: false, message: 'Missing required connection parameters' },
        { status: 400 }
      );
    }

    // Establish the connection
    await sshDockerService.connect(config);

    const status = sshDockerService.getConnectionStatus(config.id);

    return NextResponse.json({
      success: true,
      data: status,
      message: 'Connected successfully'
    });
  } catch (error) {
    console.error('SSH connection failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Connection failed' 
      },
      { status: 500 }
    );
  }
}