import { NextRequest, NextResponse } from 'next/server';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

const CONFIG_DIR = join(process.cwd(), '.ssh-docker-configs');
const CONFIG_FILE = join(CONFIG_DIR, 'connections.json');

// Ensure config directory exists
if (!existsSync(CONFIG_DIR)) {
  mkdirSync(CONFIG_DIR, { recursive: true });
}

function loadConfigs(): any[] {
  try {
    if (!existsSync(CONFIG_FILE)) {
      return [];
    }
    const data = readFileSync(CONFIG_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Failed to load SSH configs:', error);
    return [];
  }
}

function saveConfigs(configs: any[]): void {
  try {
    writeFileSync(CONFIG_FILE, JSON.stringify(configs, null, 2));
  } catch (error) {
    console.error('Failed to save SSH configs:', error);
    throw new Error('Failed to save configuration');
  }
}

interface Params {
  connectionId: string;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const { connectionId } = params;
    
    if (!connectionId) {
      return NextResponse.json(
        { success: false, message: 'Connection ID is required' },
        { status: 400 }
      );
    }

    const configs = loadConfigs();
    const config = configs.find(c => c.id === connectionId);

    if (!config) {
      return NextResponse.json(
        { success: false, message: 'Configuration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('Failed to load SSH configuration:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to load configuration' 
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const { connectionId } = params;
    
    if (!connectionId) {
      return NextResponse.json(
        { success: false, message: 'Connection ID is required' },
        { status: 400 }
      );
    }

    const configs = loadConfigs();
    const filteredConfigs = configs.filter(c => c.id !== connectionId);

    if (configs.length === filteredConfigs.length) {
      return NextResponse.json(
        { success: false, message: 'Configuration not found' },
        { status: 404 }
      );
    }

    saveConfigs(filteredConfigs);

    return NextResponse.json({
      success: true,
      message: 'Configuration deleted successfully'
    });
  } catch (error) {
    console.error('Failed to delete SSH configuration:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to delete configuration' 
      },
      { status: 500 }
    );
  }
}