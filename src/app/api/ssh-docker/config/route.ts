import { NextRequest, NextResponse } from 'next/server';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { SSHConnectionConfig } from '@/types/ssh-docker';

// Simple file-based storage for SSH configurations
// In production, you'd want to use a proper database and encrypt sensitive data
const CONFIG_DIR = join(process.cwd(), '.ssh-docker-configs');
const CONFIG_FILE = join(CONFIG_DIR, 'connections.json');

// Ensure config directory exists
if (!existsSync(CONFIG_DIR)) {
  mkdirSync(CONFIG_DIR, { recursive: true });
}

interface StoredConfig extends Omit<SSHConnectionConfig, 'authentication'> {
  authentication: Omit<SSHConnectionConfig['authentication'], 'password' | 'passphrase'> & {
    // Never store actual passwords, only metadata
    hasPassword?: boolean;
    hasPassphrase?: boolean;
  };
}

function loadConfigs(): StoredConfig[] {
  try {
    if (!existsSync(CONFIG_FILE)) {
      return [];
    }
    const data = readFileSync(CONFIG_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Failed to load SSH configs:', error);
    return [];
  }
}

function saveConfigs(configs: StoredConfig[]): void {
  try {
    writeFileSync(CONFIG_FILE, JSON.stringify(configs, null, 2));
  } catch (error) {
    console.error('Failed to save SSH configs:', error);
    throw new Error('Failed to save configuration');
  }
}

function sanitizeConfig(config: SSHConnectionConfig): StoredConfig {
  // Remove sensitive data before storing
  const sanitized: StoredConfig = {
    ...config,
    authentication: {
      type: config.authentication.type,
      privateKey: config.authentication.privateKey, // Store path, not content
      agentPath: config.authentication.agentPath,
      hasPassword: !!config.authentication.password,
      hasPassphrase: !!config.authentication.passphrase,
    }
  };
  
  return sanitized;
}

export async function GET() {
  try {
    const configs = loadConfigs();
    
    return NextResponse.json({
      success: true,
      data: configs
    });
  } catch (error) {
    console.error('Failed to load SSH configurations:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to load configurations' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const config: SSHConnectionConfig = await request.json();

    // Validate required fields
    if (!config.id || !config.name || !config.host || !config.username) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    const configs = loadConfigs();
    const sanitizedConfig = sanitizeConfig(config);
    
    // Update existing or add new
    const existingIndex = configs.findIndex(c => c.id === config.id);
    if (existingIndex >= 0) {
      configs[existingIndex] = sanitizedConfig;
    } else {
      configs.push(sanitizedConfig);
    }

    saveConfigs(configs);

    return NextResponse.json({
      success: true,
      message: 'Configuration saved successfully',
      data: sanitizedConfig
    });
  } catch (error) {
    console.error('Failed to save SSH configuration:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to save configuration' 
      },
      { status: 500 }
    );
  }
}

export async function DELETE() {
  try {
    // Clear all configurations
    saveConfigs([]);

    return NextResponse.json({
      success: true,
      message: 'All configurations cleared'
    });
  } catch (error) {
    console.error('Failed to clear SSH configurations:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to clear configurations' 
      },
      { status: 500 }
    );
  }
}