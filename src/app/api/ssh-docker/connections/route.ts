import { NextRequest, NextResponse } from 'next/server';
import { sshDockerService } from '@/services/server/ssh-docker';

export async function GET() {
  try {
    const connections = sshDockerService.getAllConnections();

    return NextResponse.json({
      success: true,
      data: connections
    });
  } catch (error) {
    console.error('Failed to list connections:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to list connections' 
      },
      { status: 500 }
    );
  }
}