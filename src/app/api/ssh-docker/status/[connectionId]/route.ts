import { NextRequest, NextResponse } from 'next/server';
import { sshDockerService } from '@/services/server/ssh-docker';

interface Params {
  connectionId: string;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const { connectionId } = params;

    if (!connectionId) {
      return NextResponse.json(
        { success: false, message: 'Connection ID is required' },
        { status: 400 }
      );
    }

    const status = sshDockerService.getConnectionStatus(connectionId);

    if (!status) {
      return NextResponse.json(
        { success: false, message: 'Connection not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('Failed to get connection status:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to get status' 
      },
      { status: 500 }
    );
  }
}