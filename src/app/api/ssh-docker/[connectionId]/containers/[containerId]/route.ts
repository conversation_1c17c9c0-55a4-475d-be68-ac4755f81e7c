import { NextRequest, NextResponse } from 'next/server';
import { sshDockerService } from '@/services/server/ssh-docker';

interface Params {
  connectionId: string;
  containerId: string;
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const { connectionId, containerId } = params;

    if (!connectionId || !containerId) {
      return NextResponse.json(
        { success: false, message: 'Connection ID and Container ID are required' },
        { status: 400 }
      );
    }

    if (!sshDockerService.isConnected(connectionId)) {
      return NextResponse.json(
        { success: false, message: 'SSH connection not active' },
        { status: 400 }
      );
    }

    await sshDockerService.removeRemoteContainer(connectionId, containerId);

    return NextResponse.json({
      success: true,
      message: 'Container removed successfully'
    });
  } catch (error) {
    console.error('Failed to remove remote container:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to remove container' 
      },
      { status: 500 }
    );
  }
}