import { NextRequest, NextResponse } from 'next/server';
import { sshDockerService } from '@/services/server/ssh-docker';

interface Params {
  connectionId: string;
  containerId: string;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const { connectionId, containerId } = params;
    const { searchParams } = new URL(request.url);
    const tail = parseInt(searchParams.get('tail') || '100');

    if (!connectionId || !containerId) {
      return NextResponse.json(
        { success: false, message: 'Connection ID and Container ID are required' },
        { status: 400 }
      );
    }

    if (!sshDockerService.isConnected(connectionId)) {
      return NextResponse.json(
        { success: false, message: 'SSH connection not active' },
        { status: 400 }
      );
    }

    const logs = await sshDockerService.getRemoteContainerLogs(
      connectionId, 
      containerId, 
      tail
    );

    return NextResponse.json({
      success: true,
      data: logs
    });
  } catch (error) {
    console.error('Failed to get remote container logs:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to get logs' 
      },
      { status: 500 }
    );
  }
}