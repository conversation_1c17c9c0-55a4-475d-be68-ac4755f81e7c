import { NextRequest, NextResponse } from 'next/server';
import { sshDockerService } from '@/services/server/ssh-docker';

interface Params {
  connectionId: string;
  containerId: string;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const { connectionId, containerId } = params;

    if (!connectionId || !containerId) {
      return NextResponse.json(
        { success: false, message: 'Connection ID and Container ID are required' },
        { status: 400 }
      );
    }

    if (!sshDockerService.isConnected(connectionId)) {
      return NextResponse.json(
        { success: false, message: 'SSH connection not active' },
        { status: 400 }
      );
    }

    await sshDockerService.startRemoteContainer(connectionId, containerId);

    return NextResponse.json({
      success: true,
      message: 'Container started successfully'
    });
  } catch (error) {
    console.error('Failed to start remote container:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to start container' 
      },
      { status: 500 }
    );
  }
}