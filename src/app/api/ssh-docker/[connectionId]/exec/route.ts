import { NextRequest, NextResponse } from 'next/server';
import { sshDockerService } from '@/services/server/ssh-docker';
import { RemoteDockerCommand } from '@/types/ssh-docker';

interface Params {
  connectionId: string;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const { connectionId } = params;
    const command: RemoteDockerCommand = await request.json();

    if (!connectionId) {
      return NextResponse.json(
        { success: false, message: 'Connection ID is required' },
        { status: 400 }
      );
    }

    if (!command.command) {
      return NextResponse.json(
        { success: false, message: 'Command is required' },
        { status: 400 }
      );
    }

    if (!sshDockerService.isConnected(connectionId)) {
      return NextResponse.json(
        { success: false, message: 'SSH connection not active' },
        { status: 400 }
      );
    }

    const result = await sshDockerService.execRemoteCommand(connectionId, command);

    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Failed to execute remote command:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to execute command' 
      },
      { status: 500 }
    );
  }
}