import { NextRequest, NextResponse } from 'next/server';
import { sshDockerService } from '@/services/server/ssh-docker';

interface Params {
  connectionId: string;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const { connectionId } = params;
    const { imageName } = await request.json();

    if (!connectionId) {
      return NextResponse.json(
        { success: false, message: 'Connection ID is required' },
        { status: 400 }
      );
    }

    if (!imageName) {
      return NextResponse.json(
        { success: false, message: 'Image name is required' },
        { status: 400 }
      );
    }

    if (!sshDockerService.isConnected(connectionId)) {
      return NextResponse.json(
        { success: false, message: 'SSH connection not active' },
        { status: 400 }
      );
    }

    await sshDockerService.pullRemoteImage(connectionId, imageName);

    return NextResponse.json({
      success: true,
      message: 'Image pulled successfully'
    });
  } catch (error) {
    console.error('Failed to pull remote image:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to pull image' 
      },
      { status: 500 }
    );
  }
}