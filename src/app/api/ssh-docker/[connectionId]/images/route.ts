import { NextRequest, NextResponse } from 'next/server';
import { sshDockerService } from '@/services/server/ssh-docker';

interface Params {
  connectionId: string;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const { connectionId } = params;

    if (!connectionId) {
      return NextResponse.json(
        { success: false, message: 'Connection ID is required' },
        { status: 400 }
      );
    }

    if (!sshDockerService.isConnected(connectionId)) {
      return NextResponse.json(
        { success: false, message: 'SSH connection not active' },
        { status: 400 }
      );
    }

    const images = await sshDockerService.listRemoteImages(connectionId);

    return NextResponse.json({
      success: true,
      data: images
    });
  } catch (error) {
    console.error('Failed to list remote images:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to list images' 
      },
      { status: 500 }
    );
  }
}