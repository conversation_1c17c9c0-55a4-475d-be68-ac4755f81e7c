import { NextRequest, NextResponse } from 'next/server';
import { CreateContainerOptions } from '@/types/docker';
import { authService } from '@/services/appwrite';

// Helper function to get session from request
async function getAuthenticatedUser(request: NextRequest) {
  try {
    const sessionId = request.headers.get('x-appwrite-session') || 
                     request.cookies.get('appwrite-session')?.value;
    
    if (!sessionId) {
      return { success: false, error: 'No session found' };
    }

    const userResult = await authService.getCurrentUser(sessionId);
    
    if (!userResult.success || !userResult.data) {
      return { success: false, error: 'Invalid session' };
    }

    return { success: true, user: userResult.data };
  } catch (error) {
    return { success: false, error: 'Authentication failed' };
  }
}

// GET /api/docker/containers - List all containers
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getAuthenticatedUser(request);
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          message: authResult.error,
        },
        { status: 401 }
      );
    }

    // Try to use Docker wrapper service
    try {
      const { dockerService } = await import('@/services/server/docker-wrapper');
      const containers = await dockerService.listContainers();
      
      return NextResponse.json({
        success: true,
        data: containers,
        count: containers.length,
      });
    } catch (dockerError) {
      console.warn('Docker service not available:', dockerError);
      return NextResponse.json({
        success: true,
        data: [],
        count: 0,
      });
    }
  } catch (error) {
    console.error('Error listing containers:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to list containers',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/docker/containers - Create a new container
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getAuthenticatedUser(request);
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          message: authResult.error,
        },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate required fields
    if (!body.image) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'Container image is required',
        },
        { status: 400 }
      );
    }

    // Try to create container with Docker service
    try {
      const { dockerService } = await import('@/services/server/docker-wrapper');

      const options: CreateContainerOptions = {
        name: body.name,
        image: body.image,
        cmd: body.cmd,
        workingDir: body.workingDir,
        user: body.user,
        ports: body.ports,
        volumes: body.volumes,
        environment: body.environment,
        cpu: body.cpu,
        memory: body.memory,
        networkMode: body.networkMode,
        privileged: body.privileged,
        autoRemove: body.autoRemove,
      };

      const containerId = await dockerService.createContainer(options);
      
      return NextResponse.json({
        success: true,
        data: {
          id: containerId,
          message: 'Container created successfully',
        },
      }, { status: 201 });

    } catch (dockerError) {
      console.error('Docker service not available:', dockerError);
      return NextResponse.json(
        {
          success: false,
          error: 'Docker service unavailable',
          message: 'Unable to create container. Docker service is not available.',
        },
        { status: 503 }
      );
    }

  } catch (error) {
    console.error('Error creating container:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create container',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}