import { NextRequest, NextResponse } from 'next/server';
import { authService } from '@/services/appwrite';

// Helper function to get session from request
async function getAuthenticatedUser(request: NextRequest) {
  try {
    const sessionId = request.headers.get('x-appwrite-session') || 
                     request.cookies.get('appwrite-session')?.value;
    
    if (!sessionId) {
      return { success: false, error: 'No session found' };
    }

    const userResult = await authService.getCurrentUser(sessionId);
    
    if (!userResult.success || !userResult.data) {
      return { success: false, error: 'Invalid session' };
    }

    return { success: true, user: userResult.data };
  } catch (error) {
    return { success: false, error: 'Authentication failed' };
  }
}

// GET /api/docker/containers/[id] - Get container details
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Authenticate user
    const authResult = await getAuthenticatedUser(request);
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          message: authResult.error,
        },
        { status: 401 }
      );
    }

    const containerId = params.id;

    if (!containerId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid container ID',
          message: 'Container ID is required',
        },
        { status: 400 }
      );
    }

    // Try to get container with Docker service
    try {
      const { dockerService } = await import('@/services/server/docker-wrapper');
      const container = await dockerService.getContainer(containerId);
      
      if (!container) {
        return NextResponse.json(
          {
            success: false,
            error: 'Container not found',
            message: `Container with ID ${containerId} not found`,
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: container,
      });

    } catch (dockerError) {
      console.warn('Docker service not available:', dockerError);
      return NextResponse.json(
        {
          success: false,
          error: 'Docker service unavailable',
          message: 'Unable to get container details. Docker service is not available.',
        },
        { status: 503 }
      );
    }

  } catch (error) {
    console.error('Error getting container:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get container',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/docker/containers/[id] - Remove container
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Authenticate user
    const authResult = await getAuthenticatedUser(request);
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          message: authResult.error,
        },
        { status: 401 }
      );
    }

    const containerId = params.id;
    const body = await request.json().catch(() => ({}));
    const force = body.force || false;

    if (!containerId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid container ID',
          message: 'Container ID is required',
        },
        { status: 400 }
      );
    }

    // Try to remove container with Docker service
    try {
      const { dockerService } = await import('@/services/server/docker-wrapper');
      await dockerService.removeContainer(containerId, force);
      
      return NextResponse.json({
        success: true,
        message: 'Container removed successfully',
      });

    } catch (dockerError) {
      console.error('Docker service error:', dockerError);
      return NextResponse.json(
        {
          success: false,
          error: 'Docker service error',
          message: dockerError instanceof Error ? dockerError.message : 'Unable to remove container',
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error removing container:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to remove container',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}