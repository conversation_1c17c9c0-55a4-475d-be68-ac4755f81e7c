import { NextRequest, NextResponse } from 'next/server';
import { authService } from '@/services/appwrite';

// Helper function to get session from request
async function getAuthenticatedUser(request: NextRequest) {
  try {
    const sessionId = request.headers.get('x-appwrite-session') || 
                     request.cookies.get('appwrite-session')?.value;
    
    if (!sessionId) {
      return { success: false, error: 'No session found' };
    }

    const userResult = await authService.getCurrentUser(sessionId);
    
    if (!userResult.success || !userResult.data) {
      return { success: false, error: 'Invalid session' };
    }

    return { success: true, user: userResult.data };
  } catch (error) {
    return { success: false, error: 'Authentication failed' };
  }
}

// POST /api/docker/containers/[id]/restart - Restart container
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Authenticate user
    const authResult = await getAuthenticatedUser(request);
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          message: authResult.error,
        },
        { status: 401 }
      );
    }

    const containerId = params.id;

    if (!containerId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid container ID',
          message: 'Container ID is required',
        },
        { status: 400 }
      );
    }

    // Try to restart container with Docker service
    try {
      const { dockerService } = await import('@/services/server/docker-wrapper');
      await dockerService.restartContainer(containerId);
      
      return NextResponse.json({
        success: true,
        message: 'Container restarted successfully',
      });

    } catch (dockerError) {
      console.error('Docker service error:', dockerError);
      return NextResponse.json(
        {
          success: false,
          error: 'Docker service error',
          message: dockerError instanceof Error ? dockerError.message : 'Unable to restart container',
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error restarting container:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to restart container',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}