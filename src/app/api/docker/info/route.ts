import { NextRequest, NextResponse } from 'next/server';
import { authService } from '@/services/appwrite';

// Helper function to get session from request
async function getAuthenticatedUser(request: NextRequest) {
  try {
    const sessionId = request.headers.get('x-appwrite-session') || 
                     request.cookies.get('appwrite-session')?.value;
    
    if (!sessionId) {
      return { success: false, error: 'No session found' };
    }

    const userResult = await authService.getCurrentUser(sessionId);
    
    if (!userResult.success || !userResult.data) {
      return { success: false, error: 'Invalid session' };
    }

    return { success: true, user: userResult.data };
  } catch (error) {
    return { success: false, error: 'Authentication failed' };
  }
}

// GET /api/docker/info - Get Docker system information
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getAuthenticatedUser(request);
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          message: authResult.error,
        },
        { status: 401 }
      );
    }

    // Try to get system info with Docker service
    try {
      const { dockerService } = await import('@/services/server/docker-wrapper');
      const systemInfo = await dockerService.getSystemInfo();
      
      return NextResponse.json({
        success: true,
        data: {
          ...systemInfo,
          connected: true,
          timestamp: new Date().toISOString()
        }
      });

    } catch (dockerError) {
      console.warn('Docker service not available:', dockerError);
      return NextResponse.json({
        success: true,
        data: {
          connected: false,
          error: 'Docker service unavailable',
          containers: 0,
          images: 0,
          serverVersion: 'N/A',
          timestamp: new Date().toISOString()
        }
      });
    }

  } catch (error) {
    console.error('Error getting system info:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get system info',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}