import { NextRequest, NextResponse } from 'next/server';

// GET /api/docker/ping - Check if Docker service is available
export async function GET(request: NextRequest) {
  try {
    // Try to use Docker wrapper service
    const { dockerService } = await import('@/services/server/docker-wrapper');
    
    const isConnected = await dockerService.ping();
    
    return NextResponse.json({
      success: true,
      data: {
        connected: isConnected,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.warn('Docker service not available:', error);
    return NextResponse.json({
      success: false,
      data: {
        connected: false,
        error: 'Docker service unavailable',
        timestamp: new Date().toISOString()
      }
    });
  }
}