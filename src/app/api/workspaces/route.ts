import { NextRequest, NextResponse } from 'next/server';
import { CreateWorkspaceOptions } from '@/types/docker';
import { getAuthService } from '@/services/appwrite';

// Helper function to get session from request
async function getAuthenticatedUser(request: NextRequest) {
  try {
    const sessionId = request.headers.get('x-appwrite-session') || 
                     request.cookies.get('appwrite-session')?.value;
    
    if (!sessionId) {
      return { success: false, error: 'No session found' };
    }

    const userResult = await getAuthService().getCurrentUser(sessionId);
    
    if (!userResult.success || !userResult.data) {
      return { success: false, error: 'Invalid session' };
    }

    return { success: true, user: userResult.data };
  } catch (error) {
    return { success: false, error: 'Authentication failed' };
  }
}

// GET /api/workspaces - List all workspace containers
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getAuthenticatedUser(request);
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          message: authResult.error,
        },
        { status: 401 }
      );
    }

    const userId = authResult.user!.userId;

    // Try to get workspaces with Docker service
    let workspaces = [];
    let dockerMessage = null;
    
    try {
      const { dockerService } = await import('@/services/server/docker-wrapper');
      
      // First check if Docker is accessible
      const dockerPing = await dockerService.ping();
      if (!dockerPing) {
        dockerMessage = 'Docker service is not accessible. Please ensure Docker is running and accessible.';
        workspaces = [];
      } else {
        const allWorkspaces = await dockerService.listWorkspaces();
        workspaces = allWorkspaces.filter(workspace => workspace.userId === userId);
      }
    } catch (dockerError) {
      console.warn('Docker service error:', dockerError);
      const errorMessage = dockerError instanceof Error ? dockerError.message : 'Unknown error';
      
      if (errorMessage.includes('permission denied')) {
        dockerMessage = 'Docker access denied. Please check Docker daemon permissions.';
      } else if (errorMessage.includes('connect:') || errorMessage.includes('ECONNREFUSED')) {
        dockerMessage = 'Cannot connect to Docker daemon. Please ensure Docker is running.';
      } else {
        dockerMessage = 'Docker service is temporarily unavailable.';
      }
      
      workspaces = [];
    }
    
    return NextResponse.json({
      success: true,
      data: workspaces,
      count: workspaces.length,
    });
  } catch (error) {
    console.error('Error listing workspaces:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to list workspaces',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/workspaces - Create a new workspace container
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getAuthenticatedUser(request);
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          message: authResult.error,
        },
        { status: 401 }
      );
    }

    const userId = authResult.user!.userId;
    const body = await request.json();
    
    // Validate required fields
    if (!body.workspaceType) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'Workspace type is required',
        },
        { status: 400 }
      );
    }

    // Validate workspace type
    const validTypes = ['ubuntu-desktop', 'development-env', 'minimal-desktop'];
    if (!validTypes.includes(body.workspaceType)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid workspace type',
          message: `Workspace type must be one of: ${validTypes.join(', ')}`,
        },
        { status: 400 }
      );
    }

    // Try to create workspace with Docker service
    try {
      const { dockerService } = await import('@/services/server/docker-wrapper');
      const { guacamoleService } = await import('@/services/guacamole');

      const options: CreateWorkspaceOptions = {
        workspaceType: body.workspaceType,
        userId: userId, // Use authenticated user ID
        name: body.name,
        vncPassword: body.vncPassword,
        displayWidth: body.displayWidth,
        displayHeight: body.displayHeight,
        resources: body.resources,
        environment: body.environment,
      };

      // Create the workspace container
      const containerId = await dockerService.createWorkspace(options);
      
      // Start the container
      await dockerService.startContainer(containerId);
      
      // Get workspace info
      const workspaceInfo = await dockerService.getWorkspaceInfo(containerId);
      if (!workspaceInfo) {
        throw new Error('Failed to get workspace information after creation');
      }

      // Create Guacamole connection
      let guacamoleConnectionId: string | undefined;
      try {
        // Ensure user exists in Guacamole
        await guacamoleService.createUser(userId, body.userPassword || 'defaultpass');
        
        // Create connection in Guacamole
        guacamoleConnectionId = await guacamoleService.createWorkspaceConnection(
          containerId,
          workspaceInfo.name,
          'localhost', // Container hostname - adjust based on your network setup
          workspaceInfo.vncPort,
          workspaceInfo.vncPassword || '',
          userId
        );

        // Update container with Guacamole connection ID
        const container = dockerService.docker.getContainer(containerId);
        await container.update({
          Labels: {
            'omnispace.guacamole.connection': guacamoleConnectionId,
          },
        });
      } catch (guacamoleError) {
        console.warn('Failed to create Guacamole connection:', guacamoleError);
        // Continue without Guacamole integration
      }
      
      return NextResponse.json({
        success: true,
        data: {
          id: containerId,
          workspace: workspaceInfo,
          guacamoleConnectionId,
          message: 'Workspace created successfully',
        },
      }, { status: 201 });

    } catch (dockerError) {
      console.error('Docker service not available:', dockerError);
      return NextResponse.json(
        {
          success: false,
          error: 'Docker service unavailable',
          message: 'Unable to create workspace. Docker service is not available.',
        },
        { status: 503 }
      );
    }

  } catch (error) {
    console.error('Error creating workspace:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create workspace',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
