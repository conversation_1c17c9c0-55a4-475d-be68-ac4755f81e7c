'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { SSHConnectionManager } from '@/components/ssh-docker/ssh-connection-manager';
import { RemoteContainersView } from '@/components/ssh-docker/remote-containers-view';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Server, HardDrive, Network, Terminal } from 'lucide-react';

export default function SSHDockerPage() {
  const [selectedConnectionId, setSelectedConnectionId] = useState<string | null>(null);
  const [selectedConnectionName, setSelectedConnectionName] = useState<string | null>(null);

  const handleConnectionSelect = (connectionId: string) => {
    setSelectedConnectionId(connectionId);
    // You might want to fetch the connection name here
    setSelectedConnectionName(connectionId);
  };

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">SSH Docker Management</h1>
        <p className="text-muted-foreground">
          Connect to remote servers and manage Docker containers via SSH
        </p>
      </div>

      <Tabs defaultValue="connections" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="connections" className="flex items-center gap-2">
            <Server className="h-4 w-4" />
            Connections
          </TabsTrigger>
          <TabsTrigger value="containers" className="flex items-center gap-2" disabled={!selectedConnectionId}>
            <HardDrive className="h-4 w-4" />
            Remote Containers
          </TabsTrigger>
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Network className="h-4 w-4" />
            Overview
          </TabsTrigger>
        </TabsList>

        <TabsContent value="connections">
          <SSHConnectionManager
            onConnectionSelect={handleConnectionSelect}
            selectedConnectionId={selectedConnectionId || undefined}
          />
        </TabsContent>

        <TabsContent value="containers">
          {selectedConnectionId ? (
            <RemoteContainersView
              connectionId={selectedConnectionId}
              connectionName={selectedConnectionName || undefined}
            />
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Server className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">No Connection Selected</h3>
                <p className="text-muted-foreground">
                  Please select a connection from the Connections tab to view containers
                </p>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center gap-2">
                  <Server className="h-5 w-5" />
                  SSH Connections
                </CardTitle>
                <CardDescription>
                  Manage secure connections to remote Docker hosts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Multiple authentication methods (password, key, agent)</li>
                  <li>• Connection health monitoring</li>
                  <li>• Automatic reconnection</li>
                  <li>• Connection pooling</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center gap-2">
                  <HardDrive className="h-5 w-5" />
                  Remote Docker
                </CardTitle>
                <CardDescription>
                  Full Docker management over SSH connections
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Container lifecycle management</li>
                  <li>• Real-time logs and stats</li>
                  <li>• Image management</li>
                  <li>• Command execution</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center gap-2">
                  <Terminal className="h-5 w-5" />
                  Security Features
                </CardTitle>
                <CardDescription>
                  Enterprise-grade security and monitoring
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Encrypted SSH connections</li>
                  <li>• Key-based authentication</li>
                  <li>• Connection logging</li>
                  <li>• Session management</li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle>Getting Started</CardTitle>
                <CardDescription>
                  Follow these steps to set up your first SSH Docker connection
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center text-xs font-medium text-blue-600 dark:text-blue-400">
                      1
                    </div>
                    <div>
                      <h4 className="font-medium">Prepare Your Server</h4>
                      <p className="text-sm text-muted-foreground">
                        Ensure Docker is installed and SSH access is configured on your remote server
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center text-xs font-medium text-blue-600 dark:text-blue-400">
                      2
                    </div>
                    <div>
                      <h4 className="font-medium">Add Connection</h4>
                      <p className="text-sm text-muted-foreground">
                        Go to the Connections tab and click "Add Connection" to configure SSH access
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center text-xs font-medium text-blue-600 dark:text-blue-400">
                      3
                    </div>
                    <div>
                      <h4 className="font-medium">Test & Connect</h4>
                      <p className="text-sm text-muted-foreground">
                        Test your connection settings and establish a secure SSH tunnel
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center text-xs font-medium text-blue-600 dark:text-blue-400">
                      4
                    </div>
                    <div>
                      <h4 className="font-medium">Manage Containers</h4>
                      <p className="text-sm text-muted-foreground">
                        Switch to Remote Containers tab to manage Docker containers on your server
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}