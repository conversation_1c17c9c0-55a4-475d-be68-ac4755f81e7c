"use client";

import React, { useState } from 'react';
import { WorkspaceMonitor } from '@/components/workspace';
import { WorkspaceInfo } from '@/types/docker';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Activity, 
  Cpu, 
  HardDrive, 
  Monitor, 
  RefreshCw,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';

export default function MonitoringPage() {
  const [workspaces, setWorkspaces] = useState<WorkspaceInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [systemStats, setSystemStats] = useState({
    totalCpuCores: 16,
    totalMemoryGB: 32,
    usedCpuCores: 0,
    usedMemoryGB: 0,
    networkThroughput: 0,
    diskUsage: 0,
    uptime: 0
  });

  // Mock user ID - in real app, this would come from auth context
  const userId = 'demo-user';

  // Fetch workspaces and system stats
  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/workspaces?userId=${userId}`);
      const data = await response.json();
      
      if (data.success) {
        const workspaceData = data.data as WorkspaceInfo[];
        setWorkspaces(workspaceData);
        
        // Calculate system stats
        const runningWorkspaces = workspaceData.filter(w => w.status === 'running');
        const usedCpu = runningWorkspaces.reduce((sum, w) => sum + w.resources.cpuLimit, 0);
        const usedMemory = runningWorkspaces.reduce((sum, w) => sum + w.resources.memoryLimit, 0);
        
        setSystemStats(prev => ({
          ...prev,
          usedCpuCores: usedCpu,
          usedMemoryGB: Math.round(usedMemory / 1024 * 100) / 100,
          networkThroughput: Math.random() * 100, // Mock data
          diskUsage: Math.random() * 80, // Mock data
          uptime: Math.floor(Date.now() / 1000) // Mock uptime
        }));
      }
    } catch (error) {
      console.error('Error fetching monitoring data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    
    // Set up periodic refresh
    const interval = setInterval(fetchData, 10000); // Refresh every 10 seconds
    
    return () => clearInterval(interval);
  }, [userId]);

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const getHealthStatus = () => {
    const cpuUsage = (systemStats.usedCpuCores / systemStats.totalCpuCores) * 100;
    const memoryUsage = (systemStats.usedMemoryGB / systemStats.totalMemoryGB) * 100;
    
    if (cpuUsage > 90 || memoryUsage > 90) {
      return { status: 'critical', color: 'text-red-500', icon: AlertTriangle };
    } else if (cpuUsage > 70 || memoryUsage > 70) {
      return { status: 'warning', color: 'text-yellow-500', icon: AlertTriangle };
    }
    return { status: 'healthy', color: 'text-green-500', icon: CheckCircle };
  };

  const health = getHealthStatus();
  const HealthIcon = health.icon;

  return (
    
      <div className="flex flex-1 flex-col gap-4 p-6">{/* Removed header - now handled by DashboardHeader */}

        {/* System Overview Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
              <Cpu className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {((systemStats.usedCpuCores / systemStats.totalCpuCores) * 100).toFixed(1)}%
              </div>
              <p className="text-xs text-muted-foreground">
                {systemStats.usedCpuCores} of {systemStats.totalCpuCores} cores
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
              <HardDrive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {((systemStats.usedMemoryGB / systemStats.totalMemoryGB) * 100).toFixed(1)}%
              </div>
              <p className="text-xs text-muted-foreground">
                {systemStats.usedMemoryGB}GB of {systemStats.totalMemoryGB}GB
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Workspaces</CardTitle>
              <Monitor className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {workspaces.filter(w => w.status === 'running').length}
              </div>
              <p className="text-xs text-muted-foreground">
                of {workspaces.length} total
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatUptime(systemStats.uptime)}
              </div>
              <p className="text-xs text-muted-foreground">
                Since last restart
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Performance Metrics */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Performance Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Network Throughput</span>
                    <span className="text-sm text-gray-600">
                      {systemStats.networkThroughput.toFixed(1)} MB/s
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(systemStats.networkThroughput, 100)}%` }}
                    />
                  </div>
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Disk Usage</span>
                    <span className="text-sm text-gray-600">
                      {systemStats.diskUsage.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${systemStats.diskUsage}%` }}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                System Health
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Overall Status</span>
                  <Badge variant={health.status === 'healthy' ? 'default' : 'destructive'}>
                    <HealthIcon className="h-3 w-3 mr-1" />
                    {health.status.toUpperCase()}
                  </Badge>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span>Docker Service</span>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Guacamole Gateway</span>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Database Connection</span>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Network Connectivity</span>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Workspace Monitoring */}
        <WorkspaceMonitor
          workspaces={workspaces}
          onRefresh={fetchData}
        />
      </div>
    
  );
}
