"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardOverview } from "@/components/dashboard/DashboardOverview";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { useAuth } from '@/contexts/auth-context';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';

export default function DashboardPage() {
  const router = useRouter();
  const { isAdmin, profile, isLoading } = useAuth();

  useEffect(() => {
    // Redirect admin users to admin dashboard
    if (!isLoading && profile && isAdmin()) {
      router.push('/admin');
    }
  }, [isAdmin, profile, isLoading, router]);

  return (
    <div className="flex flex-1 flex-col gap-4 p-4">
      {/* Header */}
      <header className="bg-background sticky top-0 flex shrink-0 items-center gap-2 border-b p-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
        <div>
          <h1 className="text-2xl font-bold">Dashboard Overview</h1>
          <p className="text-gray-600">Welcome to your Omnispace dashboard</p>
        </div>
      </header>

      {/* Content */}
      <div className="flex-1">
        <DashboardOverview />
      </div>
    </div>
  );
}
