"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardOverview } from "@/components/dashboard/DashboardOverview";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { useAuth } from '@/contexts/auth-context';

export default function DashboardPage() {
  const router = useRouter();
  const { isAdmin, profile, isLoading } = useAuth();

  useEffect(() => {
    // Redirect admin users to admin dashboard
    if (!isLoading && profile && isAdmin()) {
      router.push('/admin');
    }
  }, [isAdmin, profile, isLoading, router]);

  return (
    
      <DashboardOverview />
    
  );
}
