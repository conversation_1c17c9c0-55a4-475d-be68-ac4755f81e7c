"use client";

import React from 'react';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CreditCard, 
  DollarSign, 
  Calendar, 
  Download,
  TrendingUp,
  Package,
  Users,
  HardDrive,
  Monitor,
  Crown,
  CheckCircle,
  AlertTriangle,
  Plus,
  Edit3,
  ExternalLink
} from 'lucide-react';

export default function BillingSettingsPage() {
  return (
    <ProtectedRoute>
      <div className="flex flex-1 flex-col gap-4 p-4">
        {/* Header */}
        <header className="bg-background sticky top-0 flex shrink-0 items-center gap-2 border-b p-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
          <div>
            <h1 className="text-2xl font-bold">Billing & Usage</h1>
            <p className="text-gray-600">Manage your subscription and view usage metrics</p>
          </div>
        </header>

        {/* Content */}
        <div className="flex-1 space-y-6">
          {/* Current Plan */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Current Plan
              </CardTitle>
              <CardDescription>
                Your subscription details and billing information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-6">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-2xl font-bold">Professional</h3>
                    <Badge variant="default">Active</Badge>
                  </div>
                  <div className="text-3xl font-bold">
                    $49.99
                    <span className="text-lg font-normal text-muted-foreground">/month</span>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    Next billing: January 15, 2024 (15 days)
                  </p>
                </div>
                <Button>
                  <Crown className="h-4 w-4 mr-2" />
                  Upgrade Plan
                </Button>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <Monitor className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                  <div className="text-2xl font-bold">∞</div>
                  <div className="text-sm text-muted-foreground">Workspaces</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <HardDrive className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                  <div className="text-2xl font-bold">500GB</div>
                  <div className="text-sm text-muted-foreground">Storage</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <Users className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                  <div className="text-2xl font-bold">10</div>
                  <div className="text-sm text-muted-foreground">Team Members</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <Package className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                  <div className="text-2xl font-bold">100</div>
                  <div className="text-sm text-muted-foreground">Containers</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Usage Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Current Usage
              </CardTitle>
              <CardDescription>
                Monitor your resource consumption and limits
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Workspaces */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Monitor className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Active Workspaces</span>
                  </div>
                  <span className="text-sm font-medium">8 of ∞</span>
                </div>
                <div className="text-xs text-muted-foreground mb-1">Unlimited workspaces available</div>
              </div>

              {/* Storage */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <HardDrive className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Storage Used</span>
                  </div>
                  <span className="text-sm font-medium">245GB of 500GB</span>
                </div>
                <Progress value={49} className="mb-1" />
                <div className="text-xs text-muted-foreground">49% of storage limit used</div>
              </div>

              {/* Team Members */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Team Members</span>
                  </div>
                  <span className="text-sm font-medium">6 of 10</span>
                </div>
                <Progress value={60} className="mb-1" />
                <div className="text-xs text-muted-foreground">4 more members can be added</div>
              </div>

              {/* Containers */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Running Containers</span>
                  </div>
                  <span className="text-sm font-medium">24 of 100</span>
                </div>
                <Progress value={24} className="mb-1" />
                <div className="text-xs text-muted-foreground">76 more containers available</div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Methods */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Payment Methods
              </CardTitle>
              <CardDescription>
                Manage your payment methods and billing information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Primary Payment Method */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <CreditCard className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium flex items-center gap-2">
                      Visa •••• 4242
                      <Badge variant="secondary" className="text-xs">Default</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">Expires 12/2025</div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Edit3 className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                </div>
              </div>

              {/* Secondary Payment Method */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <DollarSign className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">PayPal</div>
                    <div className="text-sm text-muted-foreground"><EMAIL></div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Edit3 className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                </div>
              </div>

              <Button variant="outline" className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Add Payment Method
              </Button>
            </CardContent>
          </Card>

          {/* Billing History */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Billing History
              </CardTitle>
              <CardDescription>
                View and download your invoices and payment history
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Recent Invoices */}
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <div className="font-medium">INV-2024-001</div>
                    <div className="text-sm text-muted-foreground">January 1, 2024</div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">$49.99</div>
                    <Badge variant="default" className="text-xs">Paid</Badge>
                  </div>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <div className="font-medium">INV-2023-012</div>
                    <div className="text-sm text-muted-foreground">December 1, 2023</div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">$49.99</div>
                    <Badge variant="default" className="text-xs">Paid</Badge>
                  </div>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <div className="font-medium">INV-2023-011</div>
                    <div className="text-sm text-muted-foreground">November 1, 2023</div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">$49.99</div>
                    <Badge variant="default" className="text-xs">Paid</Badge>
                  </div>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>

                <Button variant="outline" className="w-full">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View All Invoices
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Plan Features */}
          <Card>
            <CardHeader>
              <CardTitle>Plan Features</CardTitle>
              <CardDescription>
                Features included in your Professional plan
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Unlimited workspaces</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">500GB storage</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Up to 10 team members</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Priority support</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Advanced monitoring</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Custom integrations</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  );
}
