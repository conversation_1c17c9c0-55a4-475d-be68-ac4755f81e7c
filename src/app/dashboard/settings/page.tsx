"use client";

import React, { useState } from 'react';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  User, 
  Monitor, 
  Shield, 
  Bell,
  Palette,
  Database,
  Network,
  Save,
  RefreshCw
} from 'lucide-react';

export default function SettingsPage() {
  const [settings, setSettings] = useState({
    // User Preferences
    defaultResolution: '1920x1080',
    defaultWorkspaceType: 'ubuntu-desktop',
    autoConnect: true,
    notifications: true,
    theme: 'system',
    
    // Resource Limits
    maxConcurrentWorkspaces: 5,
    autoStopIdleHours: 4,
    totalCpuLimit: 8,
    totalMemoryLimit: 16,
    
    // Security Settings
    sessionTimeout: 480,
    requireMFA: false,
    sessionRecording: true,
    clipboardSync: true,
    
    // System Settings
    guacamoleUrl: 'http://localhost:8080',
    dockerHost: 'unix:///var/run/docker.sock',
    logLevel: 'info',
    metricsEnabled: true
  });

  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    setSaving(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // In real app, this would save to API
    console.log('Saving settings:', settings);
    
    setSaving(false);
  };

  const handleReset = () => {
    // Reset to defaults
    setSettings({
      defaultResolution: '1920x1080',
      defaultWorkspaceType: 'ubuntu-desktop',
      autoConnect: true,
      notifications: true,
      theme: 'system',
      maxConcurrentWorkspaces: 5,
      autoStopIdleHours: 4,
      totalCpuLimit: 8,
      totalMemoryLimit: 16,
      sessionTimeout: 480,
      requireMFA: false,
      sessionRecording: true,
      clipboardSync: true,
      guacamoleUrl: 'http://localhost:8080',
      dockerHost: 'unix:///var/run/docker.sock',
      logLevel: 'info',
      metricsEnabled: true
    });
  };

  return (
    
      <div className="flex flex-1 flex-col gap-4 p-6">
        {/* Action buttons */}
        <div className="flex items-center justify-end gap-2 mb-4">
          <Button variant="outline" onClick={handleReset}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>

        {/* Settings Content */}
        <div className="max-w-4xl">
          <Tabs defaultValue="preferences" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="preferences" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Preferences
              </TabsTrigger>
              <TabsTrigger value="resources" className="flex items-center gap-2">
                <Monitor className="h-4 w-4" />
                Resources
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Security
              </TabsTrigger>
              <TabsTrigger value="system" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                System
              </TabsTrigger>
            </TabsList>

            <TabsContent value="preferences" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    User Preferences
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="resolution">Default Display Resolution</Label>
                      <select 
                        id="resolution"
                        className="w-full mt-1 p-2 border rounded-md"
                        value={settings.defaultResolution}
                        onChange={(e) => setSettings(prev => ({ ...prev, defaultResolution: e.target.value }))}
                      >
                        <option value="1920x1080">1920x1080 (Full HD)</option>
                        <option value="1680x1050">1680x1050 (WSXGA+)</option>
                        <option value="1440x900">1440x900 (WXGA+)</option>
                        <option value="1280x720">1280x720 (HD)</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="workspace-type">Default Workspace Type</Label>
                      <select 
                        id="workspace-type"
                        className="w-full mt-1 p-2 border rounded-md"
                        value={settings.defaultWorkspaceType}
                        onChange={(e) => setSettings(prev => ({ ...prev, defaultWorkspaceType: e.target.value }))}
                      >
                        <option value="ubuntu-desktop">Ubuntu Desktop</option>
                        <option value="development-env">Development Environment</option>
                        <option value="minimal-desktop">Minimal Desktop</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auto-connect">Auto-connect to workspace after creation</Label>
                        <p className="text-sm text-gray-600">Automatically open the workspace connection after creation</p>
                      </div>
                      <Switch
                        id="auto-connect"
                        checked={settings.autoConnect}
                        onCheckedChange={(checked) => setSettings(prev => ({ ...prev, autoConnect: checked }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="notifications">Enable desktop notifications</Label>
                        <p className="text-sm text-gray-600">Receive notifications for workspace events</p>
                      </div>
                      <Switch
                        id="notifications"
                        checked={settings.notifications}
                        onCheckedChange={(checked) => setSettings(prev => ({ ...prev, notifications: checked }))}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5" />
                    Appearance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div>
                    <Label htmlFor="theme">Theme</Label>
                    <select 
                      id="theme"
                      className="w-full mt-1 p-2 border rounded-md"
                      value={settings.theme}
                      onChange={(e) => setSettings(prev => ({ ...prev, theme: e.target.value }))}
                    >
                      <option value="system">System</option>
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                    </select>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="resources" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Monitor className="h-5 w-5" />
                    Resource Limits
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Maximum Concurrent Workspaces: {settings.maxConcurrentWorkspaces}</Label>
                      <Slider
                        value={[settings.maxConcurrentWorkspaces]}
                        onValueChange={(value) => setSettings(prev => ({ ...prev, maxConcurrentWorkspaces: value[0] }))}
                        max={10}
                        min={1}
                        step={1}
                        className="mt-2"
                      />
                    </div>
                    <div>
                      <Label>Auto-stop Idle Workspaces (hours): {settings.autoStopIdleHours}</Label>
                      <Slider
                        value={[settings.autoStopIdleHours]}
                        onValueChange={(value) => setSettings(prev => ({ ...prev, autoStopIdleHours: value[0] }))}
                        max={24}
                        min={1}
                        step={1}
                        className="mt-2"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Total CPU Limit (cores): {settings.totalCpuLimit}</Label>
                      <Slider
                        value={[settings.totalCpuLimit]}
                        onValueChange={(value) => setSettings(prev => ({ ...prev, totalCpuLimit: value[0] }))}
                        max={16}
                        min={1}
                        step={1}
                        className="mt-2"
                      />
                    </div>
                    <div>
                      <Label>Total Memory Limit (GB): {settings.totalMemoryLimit}</Label>
                      <Slider
                        value={[settings.totalMemoryLimit]}
                        onValueChange={(value) => setSettings(prev => ({ ...prev, totalMemoryLimit: value[0] }))}
                        max={64}
                        min={1}
                        step={1}
                        className="mt-2"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Security Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
                    <Input
                      id="session-timeout"
                      type="number"
                      min="30"
                      max="1440"
                      value={settings.sessionTimeout}
                      onChange={(e) => setSettings(prev => ({ ...prev, sessionTimeout: parseInt(e.target.value) }))}
                      className="mt-1"
                    />
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="mfa">Require Multi-Factor Authentication</Label>
                        <p className="text-sm text-gray-600">Require MFA for workspace access</p>
                      </div>
                      <Switch
                        id="mfa"
                        checked={settings.requireMFA}
                        onCheckedChange={(checked) => setSettings(prev => ({ ...prev, requireMFA: checked }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="session-recording">Enable session recording</Label>
                        <p className="text-sm text-gray-600">Record workspace sessions for audit purposes</p>
                      </div>
                      <Switch
                        id="session-recording"
                        checked={settings.sessionRecording}
                        onCheckedChange={(checked) => setSettings(prev => ({ ...prev, sessionRecording: checked }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="clipboard-sync">Enable clipboard synchronization</Label>
                        <p className="text-sm text-gray-600">Allow clipboard sharing between host and workspace</p>
                      </div>
                      <Switch
                        id="clipboard-sync"
                        checked={settings.clipboardSync}
                        onCheckedChange={(checked) => setSettings(prev => ({ ...prev, clipboardSync: checked }))}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="system" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Network className="h-5 w-5" />
                    Integration Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="guacamole-url">Guacamole Server URL</Label>
                    <Input
                      id="guacamole-url"
                      type="url"
                      value={settings.guacamoleUrl}
                      onChange={(e) => setSettings(prev => ({ ...prev, guacamoleUrl: e.target.value }))}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="docker-host">Docker Host</Label>
                    <Input
                      id="docker-host"
                      value={settings.dockerHost}
                      onChange={(e) => setSettings(prev => ({ ...prev, dockerHost: e.target.value }))}
                      className="mt-1"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    System Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="log-level">Log Level</Label>
                    <select 
                      id="log-level"
                      className="w-full mt-1 p-2 border rounded-md"
                      value={settings.logLevel}
                      onChange={(e) => setSettings(prev => ({ ...prev, logLevel: e.target.value }))}
                    >
                      <option value="error">Error</option>
                      <option value="warn">Warning</option>
                      <option value="info">Info</option>
                      <option value="debug">Debug</option>
                    </select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="metrics">Enable metrics collection</Label>
                      <p className="text-sm text-gray-600">Collect system and workspace metrics</p>
                    </div>
                    <Switch
                      id="metrics"
                      checked={settings.metricsEnabled}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, metricsEnabled: checked }))}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    
  );
}
