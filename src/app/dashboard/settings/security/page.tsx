"use client";

import React from 'react';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Shield, 
  Key, 
  Smartphone, 
  Lock,
  Eye,
  EyeOff,
  AlertTriangle,
  CheckCircle,
  Clock,
  Monitor,
  Globe,
  Activity,
  Download,
  RefreshCw
} from 'lucide-react';

export default function SecuritySettingsPage() {
  return (
    <ProtectedRoute>
      <div className="flex flex-1 flex-col gap-4 p-4">
        {/* Header */}
        <header className="bg-background sticky top-0 flex shrink-0 items-center gap-2 border-b p-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
          <div>
            <h1 className="text-2xl font-bold">Security Settings</h1>
            <p className="text-gray-600">Manage your account security and authentication</p>
          </div>
        </header>

        {/* Content */}
        <div className="flex-1 space-y-6">
          {/* Security Score */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Score
              </CardTitle>
              <CardDescription>
                Your current security level and recommendations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-4">
                <span className="text-2xl font-bold">85/100</span>
                <Badge variant="default">Excellent</Badge>
              </div>
              <Progress value={85} className="mb-4" />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Two-factor authentication enabled</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Strong password policy</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Login notifications active</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <span>Consider adding backup codes</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Authentication */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Authentication
              </CardTitle>
              <CardDescription>
                Manage your login credentials and authentication methods
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Password */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Lock className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">Password</div>
                    <div className="text-sm text-muted-foreground">Last changed 30 days ago</div>
                  </div>
                </div>
                <Button variant="outline">Change Password</Button>
              </div>

              {/* Two-Factor Authentication */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Smartphone className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium flex items-center gap-2">
                      Two-Factor Authentication
                      <Badge variant="default" className="text-xs">Enabled</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">Authenticator app configured</div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">Backup Codes</Button>
                  <Button variant="outline" size="sm">Configure</Button>
                </div>
              </div>

              {/* Recovery Options */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <RefreshCw className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">Recovery Options</div>
                    <div className="text-sm text-muted-foreground">Backup email and recovery codes</div>
                  </div>
                </div>
                <Button variant="outline">Manage</Button>
              </div>
            </CardContent>
          </Card>

          {/* Active Sessions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                Active Sessions
              </CardTitle>
              <CardDescription>
                Monitor and manage your active login sessions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Current Session */}
              <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/50">
                <div className="flex items-center gap-3">
                  <Monitor className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium flex items-center gap-2">
                      MacBook Pro - Chrome
                      <Badge variant="secondary" className="text-xs">Current</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      San Francisco, CA • ************* • Active now
                    </div>
                  </div>
                </div>
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>

              {/* Other Sessions */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Smartphone className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">iPhone - Safari</div>
                    <div className="text-sm text-muted-foreground">
                      San Francisco, CA • ************* • 2 hours ago
                    </div>
                  </div>
                </div>
                <Button variant="outline" size="sm">Revoke</Button>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Monitor className="h-5 w-5 text-yellow-600" />
                  <div>
                    <div className="font-medium flex items-center gap-2">
                      Windows PC - Edge
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    </div>
                    <div className="text-sm text-muted-foreground">
                      New York, NY • ************ • 1 day ago
                    </div>
                  </div>
                </div>
                <Button variant="destructive" size="sm">Revoke</Button>
              </div>

              <div className="flex justify-end">
                <Button variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Revoke All Sessions
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Security Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Preferences
              </CardTitle>
              <CardDescription>
                Configure your security and privacy settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Login Notifications</div>
                  <div className="text-sm text-muted-foreground">
                    Get notified when someone logs into your account
                  </div>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Device Tracking</div>
                  <div className="text-sm text-muted-foreground">
                    Track and monitor devices that access your account
                  </div>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Session Recording</div>
                  <div className="text-sm text-muted-foreground">
                    Record workspace sessions for security auditing
                  </div>
                </div>
                <Switch />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sessionTimeout">Session Timeout</Label>
                <select id="sessionTimeout" className="w-full p-2 border rounded-md">
                  <option value="60">1 hour</option>
                  <option value="240">4 hours</option>
                  <option value="480" selected>8 hours</option>
                  <option value="720">12 hours</option>
                  <option value="1440">24 hours</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxAttempts">Maximum Login Attempts</Label>
                <select id="maxAttempts" className="w-full p-2 border rounded-md">
                  <option value="3">3 attempts</option>
                  <option value="5" selected>5 attempts</option>
                  <option value="10">10 attempts</option>
                </select>
              </div>
            </CardContent>
          </Card>

          {/* Security Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Security Actions</CardTitle>
              <CardDescription>
                Additional security tools and reports
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button variant="outline" className="justify-start">
                  <Download className="h-4 w-4 mr-2" />
                  Download Security Report
                </Button>
                <Button variant="outline" className="justify-start">
                  <Activity className="h-4 w-4 mr-2" />
                  View Login History
                </Button>
                <Button variant="outline" className="justify-start">
                  <Globe className="h-4 w-4 mr-2" />
                  Manage IP Whitelist
                </Button>
                <Button variant="outline" className="justify-start">
                  <Key className="h-4 w-4 mr-2" />
                  Generate API Keys
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  );
}
