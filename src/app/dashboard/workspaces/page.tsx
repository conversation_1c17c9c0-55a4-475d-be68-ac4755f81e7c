"use client";

import React, { useState } from 'react';
import { WorkspaceManager, GuacamoleClient } from '@/components/workspace';
import { WorkspaceInfo } from '@/types/docker';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export default function WorkspacesPage() {
  const [selectedWorkspace, setSelectedWorkspace] = useState<WorkspaceInfo | null>(null);
  
  // Mock user ID - in real app, this would come from auth context
  const userId = 'demo-user';

  const handleWorkspaceSelect = (workspace: WorkspaceInfo) => {
    setSelectedWorkspace(workspace);
  };

  const handleDisconnect = () => {
    setSelectedWorkspace(null);
  };

  return (
    
      <div className="flex flex-1 flex-col p-6">
        {/* Back button for workspace connection */}
        {selectedWorkspace && (
          <div className="mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDisconnect}
              className="mb-2"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Workspaces
            </Button>
          </div>
        )}

        {/* Content */}
        <div className="flex-1">
          {selectedWorkspace ? (
            <GuacamoleClient
              workspace={selectedWorkspace}
              onDisconnect={handleDisconnect}
            />
          ) : (
            <WorkspaceManager
              onSelectWorkspace={handleWorkspaceSelect}
              userId={userId}
            />
          )}
        </div>
      </div>
    
  );
}
