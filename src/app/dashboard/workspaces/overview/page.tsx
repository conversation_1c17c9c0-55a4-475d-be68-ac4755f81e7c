'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Search, 
  Filter,
  MoreVertical,
  Play,
  Square,
  Trash2,
  Settings,
  ExternalLink,
  Clock,
  Users,
  HardDrive,
  Cpu,
  Code,
  Brain,
  Database,
  Smartphone,
  Palette,
  Server,
  Gamepad2,
  Cloud,
  FileText,
  Minimize2,
  Zap,
} from 'lucide-react';

import { ProtectedRoute } from '@/components/auth/protected-route';
import { WORKSPACE_LAYOUTS } from '@/components/workspace/components/WorkspaceLayout';

// Workspace type icons
const WORKSPACE_ICONS = {
  default: Code,
  aiWorkspace: Brain,
  aiCodeEditor: Zap,
  fullStackDev: Code,
  dataScience: Database,
  devOps: Server,
  mobileApp: Smartphone,
  webDesign: Palette,
  apiDevelopment: Settings,
  gamedev: Gamepad2,
  cloudNative: Cloud,
  mlEngineering: Brain,
  documentation: FileText,
  minimal: Minimize2,
} as const;

interface WorkspaceInstance {
  id: string;
  name: string;
  type: keyof typeof WORKSPACE_LAYOUTS;
  status: 'running' | 'stopped' | 'starting' | 'stopping';
  createdAt: string;
  lastAccessed: string;
  description?: string;
  tags: string[];
  resources: {
    cpu: number;
    memory: number;
    storage: number;
  };
}

// Mock workspace data
const MOCK_WORKSPACES: WorkspaceInstance[] = [
  {
    id: 'ws-1',
    name: 'My Full Stack Project',
    type: 'fullStackDev',
    status: 'running',
    createdAt: '2024-01-15T10:00:00Z',
    lastAccessed: '2024-01-20T14:30:00Z',
    description: 'E-commerce platform development',
    tags: ['react', 'nodejs', 'mongodb'],
    resources: { cpu: 2, memory: 4, storage: 20 },
  },
  {
    id: 'ws-2',
    name: 'Data Analysis Lab',
    type: 'dataScience',
    status: 'stopped',
    createdAt: '2024-01-10T09:00:00Z',
    lastAccessed: '2024-01-19T16:45:00Z',
    description: 'Customer behavior analysis',
    tags: ['python', 'jupyter', 'pandas'],
    resources: { cpu: 4, memory: 8, storage: 50 },
  },
  {
    id: 'ws-3',
    name: 'Mobile App Dev',
    type: 'mobileApp',
    status: 'running',
    createdAt: '2024-01-12T11:30:00Z',
    lastAccessed: '2024-01-20T12:15:00Z',
    description: 'React Native fitness app',
    tags: ['react-native', 'expo', 'firebase'],
    resources: { cpu: 2, memory: 6, storage: 30 },
  },
  {
    id: 'ws-4',
    name: 'AI Code Assistant',
    type: 'aiCodeEditor',
    status: 'running',
    createdAt: '2024-01-18T08:00:00Z',
    lastAccessed: '2024-01-20T15:00:00Z',
    description: 'AI-powered development environment',
    tags: ['ai', 'typescript', 'completion'],
    resources: { cpu: 1, memory: 2, storage: 10 },
  },
];

export default function WorkspaceOverviewPage() {
  const router = useRouter();
  const [workspaces, setWorkspaces] = useState<WorkspaceInstance[]>(MOCK_WORKSPACES);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);

  // Filter workspaces
  const filteredWorkspaces = workspaces.filter(workspace => {
    const matchesSearch = searchQuery === '' || 
      workspace.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      workspace.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      workspace.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = statusFilter === 'all' || workspace.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const handleCreateWorkspace = () => {
    router.push('/dashboard/workspaces/create');
  };

  const handleOpenWorkspace = (workspaceId: string) => {
    router.push(`/workspace/${workspaceId}`);
  };

  const handleStartWorkspace = async (workspaceId: string) => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setWorkspaces(prev => prev.map(ws => 
      ws.id === workspaceId ? { ...ws, status: 'running' as const } : ws
    ));
    setIsLoading(false);
  };

  const handleStopWorkspace = async (workspaceId: string) => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setWorkspaces(prev => prev.map(ws => 
      ws.id === workspaceId ? { ...ws, status: 'stopped' as const } : ws
    ));
    setIsLoading(false);
  };

  const getStatusColor = (status: WorkspaceInstance['status']) => {
    switch (status) {
      case 'running': return 'bg-green-500';
      case 'stopped': return 'bg-gray-500';
      case 'starting': return 'bg-yellow-500';
      case 'stopping': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold">My Workspaces</h1>
              <p className="text-muted-foreground">
                Manage your development environments
              </p>
            </div>
            <Button onClick={handleCreateWorkspace} className="gap-2">
              <Plus className="h-4 w-4" />
              Create Workspace
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <HardDrive className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Workspaces</p>
                    <p className="text-2xl font-bold">{workspaces.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <Play className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Running</p>
                    <p className="text-2xl font-bold">
                      {workspaces.filter(ws => ws.status === 'running').length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <Cpu className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">CPU Cores</p>
                    <p className="text-2xl font-bold">
                      {workspaces.reduce((sum, ws) => sum + ws.resources.cpu, 0)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                    <Users className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Memory (GB)</p>
                    <p className="text-2xl font-bold">
                      {workspaces.reduce((sum, ws) => sum + ws.resources.memory, 0)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <div className="flex gap-4 mb-6">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search workspaces..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex gap-2">
              {['all', 'running', 'stopped'].map(status => (
                <Button
                  key={status}
                  variant={statusFilter === status ? "default" : "outline"}
                  size="sm"
                  onClick={() => setStatusFilter(status)}
                  className="capitalize"
                >
                  {status}
                </Button>
              ))}
            </div>
          </div>

          {/* Workspace Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredWorkspaces.map((workspace, index) => {
              const Icon = WORKSPACE_ICONS[workspace.type];
              const layout = WORKSPACE_LAYOUTS[workspace.type];
              
              return (
                <motion.div
                  key={workspace.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2, delay: index * 0.05 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-primary/10 rounded-lg">
                            <Icon className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <CardTitle className="text-lg">{workspace.name}</CardTitle>
                            <p className="text-sm text-muted-foreground">
                              {layout.name}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${getStatusColor(workspace.status)}`} />
                          <Badge variant="outline" className="capitalize">
                            {workspace.status}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-4">
                      {workspace.description && (
                        <p className="text-sm text-muted-foreground">
                          {workspace.description}
                        </p>
                      )}
                      
                      {/* Tags */}
                      <div className="flex flex-wrap gap-1">
                        {workspace.tags.slice(0, 3).map(tag => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {workspace.tags.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{workspace.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                      
                      {/* Resources */}
                      <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Cpu className="h-3 w-3" />
                          {workspace.resources.cpu} CPU
                        </div>
                        <div className="flex items-center gap-1">
                          <HardDrive className="h-3 w-3" />
                          {workspace.resources.memory}GB RAM
                        </div>
                        <div className="flex items-center gap-1">
                          <HardDrive className="h-3 w-3" />
                          {workspace.resources.storage}GB
                        </div>
                      </div>
                      
                      {/* Last accessed */}
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        Last accessed {formatDate(workspace.lastAccessed)}
                      </div>
                      
                      {/* Actions */}
                      <div className="flex gap-2 pt-2">
                        <Button
                          onClick={() => handleOpenWorkspace(workspace.id)}
                          className="flex-1"
                          size="sm"
                        >
                          <ExternalLink className="h-4 w-4 mr-1" />
                          Open
                        </Button>
                        
                        {workspace.status === 'stopped' ? (
                          <Button
                            onClick={() => handleStartWorkspace(workspace.id)}
                            variant="outline"
                            size="sm"
                            disabled={isLoading}
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                        ) : (
                          <Button
                            onClick={() => handleStopWorkspace(workspace.id)}
                            variant="outline"
                            size="sm"
                            disabled={isLoading}
                          >
                            <Square className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>

          {/* Empty State */}
          {filteredWorkspaces.length === 0 && (
            <div className="text-center py-12">
              <div className="text-muted-foreground">
                <HardDrive className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">No workspaces found</p>
                <p className="text-sm mb-4">
                  {searchQuery || statusFilter !== 'all' 
                    ? 'Try adjusting your search or filters'
                    : 'Create your first workspace to get started'
                  }
                </p>
                {!searchQuery && statusFilter === 'all' && (
                  <Button onClick={handleCreateWorkspace} className="gap-2">
                    <Plus className="h-4 w-4" />
                    Create Your First Workspace
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    
  );
}
