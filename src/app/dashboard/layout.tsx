"use client";

import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { Toaster } from "@/components/ui/sonner";
import { DashboardHeader } from "@/components/dashboard";
import { ResponsiveSidebarLayout, useSidebarActions } from "@/components/navigation/responsive-sidebar-layout";
import React from "react";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { handleSidebarAction } = useSidebarActions();

  // Mock user permissions - in a real app, this would come from auth context
  const userPermissions = {
    role: 'admin' as const,
    permissions: ['read', 'write', 'admin'],
    canCreate: true,
    canEdit: true,
    canDelete: true,
    canManageUsers: true,
    canManageSettings: true,
    canViewBilling: true,
    canManageBilling: true,
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <SidebarProvider style={{ "--sidebar-width": "350px" } as React.CSSProperties}>
        <AppSidebar />
        <SidebarInset className="flex-1 flex flex-col">
          <DashboardHeader />
          <ResponsiveSidebarLayout
            userPermissions={userPermissions}
            onSidebarAction={handleSidebarAction}
            className="flex-1"
          >
            <main className="flex-1 overflow-auto">
              {children}
            </main>
          </ResponsiveSidebarLayout>
        </SidebarInset>
      </SidebarProvider>
      <Toaster />
    </div>
  );
}
