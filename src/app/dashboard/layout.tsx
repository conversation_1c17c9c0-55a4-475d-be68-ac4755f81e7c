"use client";

import React from "react";
import { Toaster } from "@/components/ui/sonner";
import { DashboardHeader } from "@/components/dashboard";
import { NavigationProvider } from "@/contexts/navigation-context";
import { ResponsiveSidebarLayout } from "@/components/navigation/responsive-sidebar-layout";
import { RoleBasedNavigation } from "@/components/navigation/role-based-navigation";
import { AccessibleSidebar } from "@/components/navigation/accessible-sidebar";
import { AnimatedSidebarWrapper } from "@/components/navigation/animated-sidebar-wrapper";
import {
  LayoutDashboard,
  Monitor,
  BarChart3,
  HardDrive,
  Cloud,
  Settings
} from "lucide-react";
import { NavigationItem, MainSidebarConfig } from "@/types/navigation";

// Main navigation configuration
const mainNavigationItems: NavigationItem[] = [
  {
    id: 'overview',
    type: 'link',
    title: 'Overview',
    href: '/dashboard',
    icon: LayoutDashboard,
    description: 'Dashboard overview and statistics',
  },
  {
    id: 'workspaces',
    type: 'group',
    title: 'Workspaces',
    icon: Monitor,
    description: 'Manage your development environments',
    collapsible: true,
    defaultExpanded: true,
    items: [
      {
        id: 'workspaces-overview',
        type: 'link',
        title: 'All Workspaces',
        href: '/dashboard/workspaces',
        description: 'View all workspaces',
      },
      {
        id: 'workspaces-create',
        type: 'link',
        title: 'Create Workspace',
        href: '/dashboard/workspaces/create',
        description: 'Create a new workspace',
        requiredPermissions: [{ action: 'write', resource: 'workspaces' }],
      },
    ],
  },
  {
    id: 'monitoring',
    type: 'link',
    title: 'Monitoring',
    href: '/dashboard/monitoring',
    icon: BarChart3,
    description: 'System monitoring and analytics',
    requiredPermissions: [{ action: 'read', resource: 'monitoring' }],
  },
  {
    id: 'containers',
    type: 'link',
    title: 'Containers',
    href: '/dashboard/containers',
    icon: HardDrive,
    description: 'Container management',
    requiredPermissions: [{ action: 'read', resource: 'containers' }],
  },
  {
    id: 'images',
    type: 'link',
    title: 'Images',
    href: '/dashboard/images',
    icon: Cloud,
    description: 'Container images and templates',
    requiredPermissions: [{ action: 'read', resource: 'images' }],
  },
  {
    id: 'separator-1',
    type: 'separator',
  } as NavigationItem,
  {
    id: 'settings',
    type: 'group',
    title: 'Settings',
    icon: Settings,
    description: 'Application settings and preferences',
    collapsible: true,
    items: [
      {
        id: 'settings-profile',
        type: 'link',
        title: 'Profile',
        href: '/dashboard/settings/profile',
        description: 'User profile settings',
      },
      {
        id: 'settings-security',
        type: 'link',
        title: 'Security',
        href: '/dashboard/settings/security',
        description: 'Security and authentication',
      },
      {
        id: 'settings-billing',
        type: 'link',
        title: 'Billing',
        href: '/dashboard/settings/billing',
        description: 'Subscription and billing',
        requiredRoles: ['admin', 'user'],
      },
      {
        id: 'settings-team',
        type: 'link',
        title: 'Team',
        href: '/dashboard/settings/team',
        description: 'Team management',
        requiredRoles: ['admin'],
      },
    ],
  },
];

const mainSidebarConfig: MainSidebarConfig = {
  id: 'main-sidebar',
  title: 'Omnispace',
  items: mainNavigationItems,
  branding: {
    logo: <Cloud className="h-6 w-6" />,
    title: 'Omnispace',
    subtitle: 'VM Platform',
  },
};

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <NavigationProvider initialMainSidebar={mainSidebarConfig}>
      <RoleBasedNavigation items={mainNavigationItems}>
        <AccessibleSidebar
          ariaLabel="Main navigation"
          ariaDescription="Navigate through the Omnispace dashboard sections"
        >
          <AnimatedSidebarWrapper variant="main">
            <ResponsiveSidebarLayout
              mainSidebarConfig={mainSidebarConfig}
              showPageSidebar={true}
              className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800"
            >
              <div className="flex flex-col h-full">
                <DashboardHeader />
                <main className="flex-1 overflow-auto p-6">
                  {children}
                </main>
              </div>
            </ResponsiveSidebarLayout>
          </AnimatedSidebarWrapper>
        </AccessibleSidebar>
      </RoleBasedNavigation>
      <Toaster />
    </NavigationProvider>
  );
}
