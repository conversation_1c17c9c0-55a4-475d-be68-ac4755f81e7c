'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  Brain, 
  MessageSquare, 
  Code,
  Lightbulb,
  Bug,
  Zap,
  Monitor,
  Sparkles,
} from 'lucide-react';

import { AIAssistantPanel } from '@/components/ai-code-editor';
import { ProtectedRoute } from '@/components/auth/protected-route';

interface WorkspaceInfo {
  id: string;
  name: string;
  status: 'running' | 'stopped' | 'starting' | 'stopping';
  type: string;
  createdAt: string;
}

export default function WorkspaceAIAssistantPage() {
  const params = useParams();
  const router = useRouter();
  const workspaceId = params.id as string;
  
  const [workspace, setWorkspace] = useState<WorkspaceInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock user ID - in real app, this would come from auth context
  const userId = 'demo-user';

  // Fetch workspace information
  useEffect(() => {
    const fetchWorkspace = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Mock API call - replace with actual API
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock workspace data
        const mockWorkspace: WorkspaceInfo = {
          id: workspaceId,
          name: `Workspace ${workspaceId}`,
          status: 'running',
          type: 'development-env',
          createdAt: new Date().toISOString(),
        };

        setWorkspace(mockWorkspace);
      } catch (err) {
        console.error('Failed to fetch workspace:', err);
        setError('Failed to load workspace information');
      } finally {
        setIsLoading(false);
      }
    };

    if (workspaceId) {
      fetchWorkspace();
    }
  }, [workspaceId]);

  const handleBackToWorkspace = () => {
    router.push(`/dashboard/workspaces`);
  };

  const handleOpenCodeEditor = () => {
    router.push(`/workspace/${workspaceId}/ai-editor`);
  };

  const handleOpenWorkspaceMonitor = () => {
    router.push(`/workspace/${workspaceId}/monitor`);
  };

  if (isLoading) {
    return (
      
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
          <Card className="w-96">
            <CardContent className="flex items-center justify-center p-8">
              <div className="text-center">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="mx-auto mb-4"
                >
                  <Brain className="h-8 w-8 text-primary" />
                </motion.div>
                <p className="text-lg font-medium">Loading AI Assistant</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Initializing workspace {workspaceId}...
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      
    );
  }

  if (error || !workspace) {
    return (
      
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
          <Card className="w-96">
            <CardHeader>
              <CardTitle className="text-center text-destructive">
                Error Loading Workspace
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground mb-4">
                {error || 'Workspace not found'}
              </p>
              <Button onClick={handleBackToWorkspace} variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Workspaces
              </Button>
            </CardContent>
          </Card>
        </div>
      
    );
  }

  return (
    
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        {/* Header */}
        <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToWorkspace}
                  className="gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back
                </Button>
                
                <Separator orientation="vertical" className="h-6" />
                
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <Brain className="h-6 w-6 text-primary" />
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full"
                    />
                  </div>
                  <div>
                    <h1 className="text-lg font-semibold">AI Assistant</h1>
                    <p className="text-sm text-muted-foreground">
                      {workspace.name} • {workspace.type}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Badge 
                  variant={workspace.status === 'running' ? 'default' : 'secondary'}
                  className="capitalize"
                >
                  {workspace.status}
                </Badge>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleOpenCodeEditor}
                  className="gap-2"
                >
                  <Code className="h-4 w-4" />
                  Code Editor
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleOpenWorkspaceMonitor}
                  className="gap-2"
                >
                  <Monitor className="h-4 w-4" />
                  Monitor
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
            {/* Welcome Panel */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="lg:col-span-1"
            >
              <Card className="h-full bg-gradient-to-br from-primary/5 via-background to-secondary/5 border-primary/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-primary" />
                    AI Assistant Features
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3 p-3 rounded-lg bg-background/50">
                      <MessageSquare className="h-5 w-5 text-blue-500 mt-0.5" />
                      <div>
                        <p className="font-medium text-sm">Interactive Chat</p>
                        <p className="text-xs text-muted-foreground">
                          Ask questions about your code and get intelligent responses
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3 p-3 rounded-lg bg-background/50">
                      <Lightbulb className="h-5 w-5 text-yellow-500 mt-0.5" />
                      <div>
                        <p className="font-medium text-sm">Code Explanation</p>
                        <p className="text-xs text-muted-foreground">
                          Get detailed explanations of complex code snippets
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3 p-3 rounded-lg bg-background/50">
                      <Bug className="h-5 w-5 text-red-500 mt-0.5" />
                      <div>
                        <p className="font-medium text-sm">Bug Detection</p>
                        <p className="text-xs text-muted-foreground">
                          Find and fix issues in your code with AI assistance
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3 p-3 rounded-lg bg-background/50">
                      <Zap className="h-5 w-5 text-purple-500 mt-0.5" />
                      <div>
                        <p className="font-medium text-sm">Code Optimization</p>
                        <p className="text-xs text-muted-foreground">
                          Get suggestions to improve performance and readability
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3 p-3 rounded-lg bg-background/50">
                      <Code className="h-5 w-5 text-green-500 mt-0.5" />
                      <div>
                        <p className="font-medium text-sm">Test Generation</p>
                        <p className="text-xs text-muted-foreground">
                          Generate unit tests for your functions and classes
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t">
                    <p className="text-xs text-muted-foreground">
                      💡 <strong>Tip:</strong> You can select code in the editor and ask 
                      specific questions about it for more targeted assistance.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* AI Assistant Panel */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="lg:col-span-2"
            >
              <Card className="h-full">
                <AIAssistantPanel
                  isOpen={true}
                  onClose={() => {}} // No close button in dedicated view
                  userId={userId}
                  workspaceId={workspaceId}
                  className="h-full"
                />
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    
  );
}
