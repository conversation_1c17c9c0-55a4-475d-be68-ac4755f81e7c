import { SSHConnectionConfig } from '@/types/ssh-docker';

// Client-side configuration helpers
export const SSH_CONFIG_STORAGE_KEY = 'ssh-docker-configs';

export interface ConfigTemplate {
  id: string;
  name: string;
  description: string;
  config: Partial<SSHConnectionConfig>;
}

// Common SSH configuration templates
export const SSH_CONFIG_TEMPLATES: ConfigTemplate[] = [
  {
    id: 'ubuntu-server',
    name: 'Ubuntu Server',
    description: 'Standard Ubuntu server with Docker',
    config: {
      port: 22,
      authentication: {
        type: 'privateKey',
        privateKey: '~/.ssh/id_rsa',
      },
      options: {
        connectTimeout: 15000,
        keepaliveInterval: 30000,
        keepaliveCountMax: 3,
      },
      dockerConfig: {
        socketPath: '/var/run/docker.sock',
      },
    },
  },
  {
    id: 'aws-ec2',
    name: 'AWS EC2 Instance',
    description: 'AWS EC2 instance with key-based authentication',
    config: {
      port: 22,
      username: 'ec2-user',
      authentication: {
        type: 'privateKey',
        privateKey: '~/.ssh/aws-key.pem',
      },
      options: {
        connectTimeout: 20000,
        keepaliveInterval: 60000,
        keepaliveCountMax: 3,
      },
      dockerConfig: {
        socketPath: '/var/run/docker.sock',
      },
    },
  },
  {
    id: 'digital-ocean',
    name: 'DigitalOcean Droplet',
    description: 'DigitalOcean droplet with SSH key',
    config: {
      port: 22,
      username: 'root',
      authentication: {
        type: 'privateKey',
        privateKey: '~/.ssh/do_rsa',
      },
      options: {
        connectTimeout: 15000,
        keepaliveInterval: 30000,
        keepaliveCountMax: 3,
      },
      dockerConfig: {
        socketPath: '/var/run/docker.sock',
      },
    },
  },
  {
    id: 'password-auth',
    name: 'Password Authentication',
    description: 'Simple password-based authentication',
    config: {
      port: 22,
      authentication: {
        type: 'password',
      },
      options: {
        connectTimeout: 10000,
        keepaliveInterval: 30000,
        keepaliveCountMax: 3,
      },
      dockerConfig: {
        socketPath: '/var/run/docker.sock',
      },
    },
  },
  {
    id: 'ssh-agent',
    name: 'SSH Agent',
    description: 'Use SSH agent for authentication',
    config: {
      port: 22,
      authentication: {
        type: 'agent',
      },
      options: {
        connectTimeout: 15000,
        keepaliveInterval: 30000,
        keepaliveCountMax: 3,
      },
      dockerConfig: {
        socketPath: '/var/run/docker.sock',
      },
    },
  },
];

// Helper functions for configuration management
export function generateConnectionId(): string {
  return `ssh-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

export function validateConfig(config: Partial<SSHConnectionConfig>): string[] {
  const errors: string[] = [];

  if (!config.name?.trim()) {
    errors.push('Connection name is required');
  }

  if (!config.host?.trim()) {
    errors.push('Host is required');
  }

  if (!config.username?.trim()) {
    errors.push('Username is required');
  }

  if (!config.port || config.port < 1 || config.port > 65535) {
    errors.push('Valid port number is required (1-65535)');
  }

  if (!config.authentication) {
    errors.push('Authentication configuration is required');
  } else {
    switch (config.authentication.type) {
      case 'password':
        if (!config.authentication.password?.trim()) {
          errors.push('Password is required for password authentication');
        }
        break;
      case 'privateKey':
        if (!config.authentication.privateKey?.trim()) {
          errors.push('Private key path is required for key authentication');
        }
        break;
      case 'agent':
        // SSH agent doesn't require additional validation
        break;
      default:
        errors.push('Valid authentication type is required');
    }
  }

  return errors;
}

export function sanitizeConfigForStorage(config: SSHConnectionConfig): any {
  // Remove sensitive data before client-side storage
  return {
    ...config,
    authentication: {
      ...config.authentication,
      password: undefined, // Never store passwords client-side
      passphrase: undefined, // Never store passphrases client-side
    },
  };
}

export function mergeConfigWithTemplate(
  template: ConfigTemplate,
  overrides: Partial<SSHConnectionConfig>
): SSHConnectionConfig {
  return {
    id: overrides.id || generateConnectionId(),
    name: overrides.name || template.name,
    host: overrides.host || '',
    port: overrides.port || template.config.port || 22,
    username: overrides.username || template.config.username || '',
    authentication: {
      ...template.config.authentication,
      ...overrides.authentication,
    } as SSHConnectionConfig['authentication'],
    dockerConfig: {
      ...template.config.dockerConfig,
      ...overrides.dockerConfig,
    },
    options: {
      ...template.config.options,
      ...overrides.options,
    },
  };
}

// Connection testing utilities
export function getConnectionDisplayName(config: SSHConnectionConfig): string {
  return `${config.name} (${config.username}@${config.host}:${config.port})`;
}

export function getAuthMethodDisplay(authType: SSHConnectionConfig['authentication']['type']): string {
  switch (authType) {
    case 'password':
      return 'Password';
    case 'privateKey':
      return 'Private Key';
    case 'agent':
      return 'SSH Agent';
    default:
      return 'Unknown';
  }
}

export function formatConnectionStatus(connected: boolean, error?: string): {
  status: 'connected' | 'disconnected' | 'error';
  message: string;
  color: string;
} {
  if (error) {
    return {
      status: 'error',
      message: error,
      color: 'text-red-600',
    };
  }

  if (connected) {
    return {
      status: 'connected',
      message: 'Connected',
      color: 'text-green-600',
    };
  }

  return {
    status: 'disconnected',
    message: 'Disconnected',
    color: 'text-gray-600',
  };
}