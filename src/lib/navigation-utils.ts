/**
 * Navigation Utilities and Helpers
 * Utility functions for navigation management, breadcrumb generation, and sidebar state persistence
 */

import { 
  NavigationItem, 
  BreadcrumbItem, 
  BreadcrumbConfig, 
  SidebarState,
  UserRole,
  Permission,
  DashboardPageConfig,
  DashboardPageId,
} from '@/types/navigation';
import { 
  LayoutDashboard, 
  Monitor, 
  BarChart3, 
  HardDrive, 
  Cloud, 
  Settings,
  User,
  Shield,
  CreditCard,
  Users,
  Building,
} from 'lucide-react';

// Storage keys
export const STORAGE_KEYS = {
  SIDEBAR_STATE: 'omnispace-sidebar-state',
  NAVIGATION_PREFERENCES: 'omnispace-navigation-preferences',
  BREADCRUMB_HISTORY: 'omnispace-breadcrumb-history',
} as const;

// Default dashboard page configurations
export const defaultDashboardPages: Record<DashboardPageId, DashboardPageConfig> = {
  'overview': {
    id: 'overview',
    title: 'Overview',
    path: '/dashboard',
    icon: LayoutDashboard,
    description: 'Dashboard overview and statistics',
    metadata: {
      keywords: ['dashboard', 'overview', 'statistics'],
      category: 'main',
      priority: 1,
    },
  },
  'workspaces': {
    id: 'workspaces',
    title: 'Workspaces',
    path: '/dashboard/workspaces',
    icon: Monitor,
    description: 'Manage development environments',
    metadata: {
      keywords: ['workspaces', 'environments', 'development'],
      category: 'main',
      priority: 2,
    },
  },
  'workspaces-create': {
    id: 'workspaces-create',
    title: 'Create Workspace',
    path: '/dashboard/workspaces/create',
    icon: Monitor,
    description: 'Create a new workspace',
    requiredRoles: ['admin', 'developer', 'user'],
    metadata: {
      keywords: ['create', 'workspace', 'new'],
      category: 'action',
      priority: 3,
    },
  },
  'workspaces-overview': {
    id: 'workspaces-overview',
    title: 'Workspace Overview',
    path: '/dashboard/workspaces/overview',
    icon: Monitor,
    description: 'Workspace overview and details',
    metadata: {
      keywords: ['workspace', 'overview', 'details'],
      category: 'detail',
      priority: 4,
    },
  },
  'monitoring': {
    id: 'monitoring',
    title: 'Monitoring',
    path: '/dashboard/monitoring',
    icon: BarChart3,
    description: 'System monitoring and analytics',
    requiredRoles: ['admin', 'developer', 'moderator'],
    metadata: {
      keywords: ['monitoring', 'analytics', 'metrics'],
      category: 'main',
      priority: 5,
    },
  },
  'containers': {
    id: 'containers',
    title: 'Containers',
    path: '/dashboard/containers',
    icon: HardDrive,
    description: 'Container management',
    requiredRoles: ['admin', 'developer'],
    metadata: {
      keywords: ['containers', 'docker', 'management'],
      category: 'main',
      priority: 6,
    },
  },
  'images': {
    id: 'images',
    title: 'Images',
    path: '/dashboard/images',
    icon: Cloud,
    description: 'Container images and templates',
    requiredRoles: ['admin', 'developer'],
    metadata: {
      keywords: ['images', 'templates', 'docker'],
      category: 'main',
      priority: 7,
    },
  },
  'settings': {
    id: 'settings',
    title: 'Settings',
    path: '/dashboard/settings',
    icon: Settings,
    description: 'Application settings',
    metadata: {
      keywords: ['settings', 'configuration', 'preferences'],
      category: 'main',
      priority: 8,
    },
  },
  'settings-profile': {
    id: 'settings-profile',
    title: 'Profile Settings',
    path: '/dashboard/settings/profile',
    icon: User,
    description: 'User profile settings',
    metadata: {
      keywords: ['profile', 'user', 'personal'],
      category: 'settings',
      priority: 9,
    },
  },
  'settings-security': {
    id: 'settings-security',
    title: 'Security Settings',
    path: '/dashboard/settings/security',
    icon: Shield,
    description: 'Security and authentication settings',
    metadata: {
      keywords: ['security', 'authentication', 'password'],
      category: 'settings',
      priority: 10,
    },
  },
  'settings-billing': {
    id: 'settings-billing',
    title: 'Billing Settings',
    path: '/dashboard/settings/billing',
    icon: CreditCard,
    description: 'Billing and subscription settings',
    requiredRoles: ['admin', 'user'],
    metadata: {
      keywords: ['billing', 'subscription', 'payment'],
      category: 'settings',
      priority: 11,
    },
  },
  'settings-integrations': {
    id: 'settings-integrations',
    title: 'Integrations',
    path: '/dashboard/settings/integrations',
    icon: Settings,
    description: 'Third-party integrations',
    requiredRoles: ['admin'],
    metadata: {
      keywords: ['integrations', 'api', 'third-party'],
      category: 'settings',
      priority: 12,
    },
  },
};

/**
 * Navigation state persistence utilities
 */
export class NavigationStateManager {
  static saveSidebarState(state: SidebarState): void {
    try {
      localStorage.setItem(STORAGE_KEYS.SIDEBAR_STATE, JSON.stringify(state));
    } catch (error) {
      console.warn('Failed to save sidebar state:', error);
    }
  }

  static loadSidebarState(): Partial<SidebarState> | null {
    try {
      const saved = localStorage.getItem(STORAGE_KEYS.SIDEBAR_STATE);
      return saved ? JSON.parse(saved) : null;
    } catch (error) {
      console.warn('Failed to load sidebar state:', error);
      return null;
    }
  }

  static clearSidebarState(): void {
    try {
      localStorage.removeItem(STORAGE_KEYS.SIDEBAR_STATE);
    } catch (error) {
      console.warn('Failed to clear sidebar state:', error);
    }
  }

  static saveNavigationPreferences(preferences: Record<string, any>): void {
    try {
      localStorage.setItem(STORAGE_KEYS.NAVIGATION_PREFERENCES, JSON.stringify(preferences));
    } catch (error) {
      console.warn('Failed to save navigation preferences:', error);
    }
  }

  static loadNavigationPreferences(): Record<string, any> | null {
    try {
      const saved = localStorage.getItem(STORAGE_KEYS.NAVIGATION_PREFERENCES);
      return saved ? JSON.parse(saved) : null;
    } catch (error) {
      console.warn('Failed to load navigation preferences:', error);
      return null;
    }
  }
}

/**
 * Breadcrumb generation utilities
 */
export class BreadcrumbGenerator {
  static generateFromPath(
    path: string, 
    config?: Partial<BreadcrumbConfig>
  ): BreadcrumbItem[] {
    const segments = path.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Add home breadcrumb
    if (config?.showHome !== false) {
      breadcrumbs.push({
        id: 'home',
        label: 'Dashboard',
        href: config?.homeHref || '/dashboard',
        icon: LayoutDashboard,
        isClickable: true,
      });
    }

    // Generate breadcrumbs for each segment
    let currentPath = '';
    segments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === segments.length - 1;
      
      // Get page config if available
      const pageConfig = Object.values(defaultDashboardPages).find(
        page => page.path === currentPath
      );

      breadcrumbs.push({
        id: segment,
        label: pageConfig?.title || this.formatSegmentLabel(segment),
        href: currentPath,
        icon: pageConfig?.icon,
        isLast,
        isClickable: !isLast,
      });
    });

    // Apply maxItems limit
    if (config?.maxItems && breadcrumbs.length > config.maxItems) {
      const start = breadcrumbs.slice(0, 1); // Keep home
      const end = breadcrumbs.slice(-(config.maxItems - 1)); // Keep last items
      return [...start, ...end];
    }

    return breadcrumbs;
  }

  static formatSegmentLabel(segment: string): string {
    return segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  static generateFromPageConfig(pageConfig: DashboardPageConfig): BreadcrumbItem[] {
    const pathSegments = pageConfig.path.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Add home
    breadcrumbs.push({
      id: 'home',
      label: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard,
      isClickable: true,
    });

    // Build breadcrumbs from path segments
    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;
      
      if (isLast) {
        breadcrumbs.push({
          id: pageConfig.id,
          label: pageConfig.title,
          href: pageConfig.path,
          icon: pageConfig.icon,
          isLast: true,
          isClickable: false,
        });
      } else {
        // Find parent page config
        const parentConfig = Object.values(defaultDashboardPages).find(
          page => page.path === currentPath
        );
        
        breadcrumbs.push({
          id: segment,
          label: parentConfig?.title || this.formatSegmentLabel(segment),
          href: currentPath,
          icon: parentConfig?.icon,
          isClickable: true,
        });
      }
    });

    return breadcrumbs;
  }
}

/**
 * Navigation item utilities
 */
export class NavigationItemUtils {
  static flattenItems(items: NavigationItem[]): NavigationItem[] {
    const flattened: NavigationItem[] = [];
    
    for (const item of items) {
      flattened.push(item);
      
      if (item.type === 'group' || item.type === 'dropdown') {
        flattened.push(...this.flattenItems(item.items));
      }
    }
    
    return flattened;
  }

  static findItemById(items: NavigationItem[], id: string): NavigationItem | undefined {
    for (const item of items) {
      if (item.id === id) return item;
      
      if (item.type === 'group' || item.type === 'dropdown') {
        const found = this.findItemById(item.items, id);
        if (found) return found;
      }
    }
    
    return undefined;
  }

  static findItemByPath(items: NavigationItem[], path: string): NavigationItem | undefined {
    for (const item of items) {
      if (item.type === 'link' && item.href === path) return item;
      
      if (item.type === 'group' || item.type === 'dropdown') {
        const found = this.findItemByPath(item.items, path);
        if (found) return found;
      }
    }
    
    return undefined;
  }

  static filterByRole(items: NavigationItem[], userRole: UserRole): NavigationItem[] {
    return items.filter(item => {
      if (item.requiredRoles && !item.requiredRoles.includes(userRole)) {
        return false;
      }
      
      if (item.type === 'group' || item.type === 'dropdown') {
        const filteredItems = this.filterByRole(item.items, userRole);
        return filteredItems.length > 0;
      }
      
      return true;
    }).map(item => {
      if (item.type === 'group' || item.type === 'dropdown') {
        return {
          ...item,
          items: this.filterByRole(item.items, userRole),
        };
      }
      return item;
    });
  }

  static filterByPermissions(
    items: NavigationItem[], 
    userPermissions: Permission[]
  ): NavigationItem[] {
    const hasPermission = (required: Permission[]): boolean => {
      return required.every(req => 
        userPermissions.some(perm => 
          (perm.action === '*' || perm.action === req.action) &&
          (perm.resource === '*' || perm.resource === req.resource)
        )
      );
    };

    return items.filter(item => {
      if (item.requiredPermissions && !hasPermission(item.requiredPermissions)) {
        return false;
      }
      
      if (item.type === 'group' || item.type === 'dropdown') {
        const filteredItems = this.filterByPermissions(item.items, userPermissions);
        return filteredItems.length > 0;
      }
      
      return true;
    }).map(item => {
      if (item.type === 'group' || item.type === 'dropdown') {
        return {
          ...item,
          items: this.filterByPermissions(item.items, userPermissions),
        };
      }
      return item;
    });
  }

  static sortByOrder(items: NavigationItem[]): NavigationItem[] {
    return [...items].sort((a, b) => {
      const orderA = a.order ?? 999;
      const orderB = b.order ?? 999;
      return orderA - orderB;
    });
  }
}

/**
 * Page configuration utilities
 */
export class PageConfigUtils {
  static getPageConfig(pageId: DashboardPageId): DashboardPageConfig | undefined {
    return defaultDashboardPages[pageId];
  }

  static getPageConfigByPath(path: string): DashboardPageConfig | undefined {
    return Object.values(defaultDashboardPages).find(page => page.path === path);
  }

  static getAllPageConfigs(): DashboardPageConfig[] {
    return Object.values(defaultDashboardPages);
  }

  static getPagesByCategory(category: string): DashboardPageConfig[] {
    return Object.values(defaultDashboardPages).filter(
      page => page.metadata?.category === category
    );
  }

  static searchPages(query: string): DashboardPageConfig[] {
    const lowercaseQuery = query.toLowerCase();
    
    return Object.values(defaultDashboardPages).filter(page => {
      const titleMatch = page.title.toLowerCase().includes(lowercaseQuery);
      const descriptionMatch = page.description?.toLowerCase().includes(lowercaseQuery);
      const keywordMatch = page.metadata?.keywords.some(keyword => 
        keyword.toLowerCase().includes(lowercaseQuery)
      );
      
      return titleMatch || descriptionMatch || keywordMatch;
    });
  }
}
