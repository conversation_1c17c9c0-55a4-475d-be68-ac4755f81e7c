'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Server,
  Play,
  Square,
  Pause,
  RotateCcw,
  Trash2,
  Terminal,
  FileText,
  Activity,
  Clock,
  Cpu,
  Network,
  HardDrive,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2,
  Download,
  Eye,
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

import { RemoteContainerInfo, RemoteDockerImage } from '@/types/ssh-docker';
import { useSSHConnection } from '@/hooks/useSSHDocker';
import { sshDockerAPIService } from '@/services/client/ssh-docker-api';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface RemoteContainersViewProps {
  connectionId: string;
  connectionName?: string;
}

export function RemoteContainersView({ 
  connectionId, 
  connectionName 
}: RemoteContainersViewProps) {
  const {
    status,
    containers,
    images,
    loading,
    error,
    refreshAll,
    refreshContainers,
  } = useSSHConnection(connectionId);

  const [selectedContainer, setSelectedContainer] = useState<RemoteContainerInfo | null>(null);
  const [containerLogs, setContainerLogs] = useState<string>('');
  const [loadingLogs, setLoadingLogs] = useState(false);
  const [actionLoading, setActionLoading] = useState<{ [key: string]: boolean }>({});

  const handleContainerAction = async (containerId: string, action: string) => {
    const actionKey = `${containerId}-${action}`;
    setActionLoading(prev => ({ ...prev, [actionKey]: true }));

    try {
      switch (action) {
        case 'start':
          await sshDockerAPIService.startRemoteContainer(connectionId, containerId);
          toast.success('Container started successfully');
          break;
        case 'stop':
          await sshDockerAPIService.stopRemoteContainer(connectionId, containerId);
          toast.success('Container stopped successfully');
          break;
        case 'remove':
          await sshDockerAPIService.removeRemoteContainer(connectionId, containerId);
          toast.success('Container removed successfully');
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }
      
      refreshContainers();
    } catch (error) {
      toast.error(`Failed to ${action} container: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setActionLoading(prev => ({ ...prev, [actionKey]: false }));
    }
  };

  const handleViewLogs = async (container: RemoteContainerInfo) => {
    setSelectedContainer(container);
    setLoadingLogs(true);
    
    try {
      const logs = await sshDockerAPIService.getRemoteContainerLogs(connectionId, container.id);
      setContainerLogs(logs);
    } catch (error) {
      toast.error(`Failed to fetch logs: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setContainerLogs('Failed to load logs');
    } finally {
      setLoadingLogs(false);
    }
  };

  const getStatusIcon = (status: RemoteContainerInfo['status']) => {
    switch (status) {
      case 'running': return CheckCircle;
      case 'stopped': return XCircle;
      case 'starting': return Loader2;
      case 'stopping': return AlertTriangle;
      case 'paused': return Pause;
      case 'restarting': return RotateCcw;
      default: return XCircle;
    }
  };

  const getStatusColor = (status: RemoteContainerInfo['status']) => {
    switch (status) {
      case 'running': return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'stopped': return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      case 'starting': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'stopping': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400';
      case 'paused': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400';
      case 'restarting': return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20 dark:text-purple-400';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleString();
  };

  if (!status?.connected) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Server className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-medium mb-2">Connection Required</h3>
          <p className="text-muted-foreground">
            Please connect to the SSH server to view containers
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-semibold">Remote Containers</h2>
          <p className="text-muted-foreground">
            Managing containers on {connectionName || connectionId}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={refreshAll} disabled={loading}>
            {loading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-4 rounded-lg bg-red-50 border border-red-200 text-red-700">
          {error}
        </div>
      )}

      {/* Connection Status */}
      <div className="mb-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Connection Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Connected</span>
              </div>
              {status.dockerAvailable && (
                <div className="flex items-center gap-2">
                  <HardDrive className="h-4 w-4" />
                  <span className="text-sm">Docker {status.dockerVersion}</span>
                </div>
              )}
              <div className="text-sm text-muted-foreground">
                {containers.length} containers, {images.length} images
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Containers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <AnimatePresence mode="popLayout">
          {containers.map((container, index) => {
            const StatusIcon = getStatusIcon(container.status);
            
            return (
              <motion.div
                key={container.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.05 }}
              >
                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <StatusIcon className={cn(
                          'h-4 w-4',
                          container.status === 'starting' || container.status === 'restarting' 
                            ? 'animate-spin' : ''
                        )} />
                        <div>
                          <CardTitle className="text-base">{container.name}</CardTitle>
                          <CardDescription>{container.image}</CardDescription>
                        </div>
                      </div>
                      <Badge className={cn('text-xs', getStatusColor(container.status))}>
                        {container.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-3">
                      {/* Container Info */}
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center justify-between">
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            Created
                          </span>
                          <span className="text-muted-foreground">
                            {formatDate(container.created)}
                          </span>
                        </div>
                        
                        {Object.keys(container.ports).length > 0 && (
                          <div className="flex items-center justify-between">
                            <span className="flex items-center gap-1">
                              <Network className="h-3 w-3" />
                              Ports
                            </span>
                            <span className="text-muted-foreground">
                              {Object.keys(container.ports).length}
                            </span>
                          </div>
                        )}

                        {container.cpu !== undefined && (
                          <div className="flex items-center justify-between">
                            <span className="flex items-center gap-1">
                              <Cpu className="h-3 w-3" />
                              CPU
                            </span>
                            <span className="text-muted-foreground">
                              {container.cpu}%
                            </span>
                          </div>
                        )}

                        {container.memory !== undefined && (
                          <div className="flex items-center justify-between">
                            <span className="flex items-center gap-1">
                              <Activity className="h-3 w-3" />
                              Memory
                            </span>
                            <span className="text-muted-foreground">
                              {container.memory}MB
                            </span>
                          </div>
                        )}
                      </div>

                      <Separator />

                      {/* Actions */}
                      <div className="flex flex-wrap gap-2">
                        {container.status === 'running' ? (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button 
                                variant="outline" 
                                size="sm"
                                disabled={actionLoading[`${container.id}-stop`]}
                              >
                                {actionLoading[`${container.id}-stop`] ? (
                                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                ) : (
                                  <Square className="h-3 w-3 mr-1" />
                                )}
                                Stop
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Stop Container</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to stop "{container.name}"?
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction 
                                  onClick={() => handleContainerAction(container.id, 'stop')}
                                >
                                  Stop
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        ) : (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleContainerAction(container.id, 'start')}
                            disabled={actionLoading[`${container.id}-start`]}
                          >
                            {actionLoading[`${container.id}-start`] ? (
                              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                            ) : (
                              <Play className="h-3 w-3 mr-1" />
                            )}
                            Start
                          </Button>
                        )}

                        <Dialog>
                          <DialogTrigger asChild>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleViewLogs(container)}
                            >
                              <FileText className="h-3 w-3 mr-1" />
                              Logs
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl">
                            <DialogHeader>
                              <DialogTitle>Container Logs - {selectedContainer?.name}</DialogTitle>
                              <DialogDescription>
                                Real-time logs from {selectedContainer?.image}
                              </DialogDescription>
                            </DialogHeader>
                            <div className="mt-4">
                              {loadingLogs ? (
                                <div className="flex items-center justify-center h-64">
                                  <Loader2 className="h-6 w-6 animate-spin" />
                                </div>
                              ) : (
                                <ScrollArea className="h-96 w-full border rounded-md p-4">
                                  <pre className="text-sm font-mono whitespace-pre-wrap">
                                    {containerLogs || 'No logs available'}
                                  </pre>
                                </ScrollArea>
                              )}
                            </div>
                          </DialogContent>
                        </Dialog>

                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button 
                              variant="outline" 
                              size="sm"
                              disabled={actionLoading[`${container.id}-remove`]}
                            >
                              {actionLoading[`${container.id}-remove`] ? (
                                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                              ) : (
                                <Trash2 className="h-3 w-3 mr-1" />
                              )}
                              Remove
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Remove Container</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to remove "{container.name}"? 
                                This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction 
                                onClick={() => handleContainerAction(container.id, 'remove')}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                Remove
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {containers.length === 0 && !loading && (
        <div className="text-center py-12">
          <HardDrive className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-medium mb-2">No containers found</h3>
          <p className="text-muted-foreground">
            There are no Docker containers on this remote server
          </p>
        </div>
      )}
    </div>
  );
}