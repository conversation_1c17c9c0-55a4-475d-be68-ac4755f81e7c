'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Server,
  Plus,
  Wifi,
  WifiOff,
  Settings,
  Trash2,
  Eye,
  EyeOff,
  TestTube,
  CheckCircle,
  XCircle,
  Loader2,
  Edit,
  Save,
  X,
  Key,
  Lock,
  Terminal,
  HardDrive,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

import { SSHConnectionConfig, SSHConnectionStatus } from '@/types/ssh-docker';
import { useSSHDocker } from '@/hooks/useSSHDocker';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface SSHConnectionManagerProps {
  onConnectionSelect?: (connectionId: string) => void;
  selectedConnectionId?: string;
}

export function SSHConnectionManager({ 
  onConnectionSelect, 
  selectedConnectionId 
}: SSHConnectionManagerProps) {
  const {
    connections,
    loading,
    error,
    testConnection,
    connect,
    disconnect,
    refreshConnections,
    saveConnectionConfig,
    getSavedConnectionConfigs,
    deleteConnectionConfig,
  } = useSSHDocker();

  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingConnection, setEditingConnection] = useState<SSHConnectionConfig | null>(null);
  const [savedConfigs, setSavedConfigs] = useState<SSHConnectionConfig[]>([]);
  const [testingConnections, setTestingConnections] = useState<Set<string>>(new Set());
  const [connectingIds, setConnectingIds] = useState<Set<string>>(new Set());

  // Load saved configs on mount
  useEffect(() => {
    loadSavedConfigs();
  }, []);

  const loadSavedConfigs = async () => {
    try {
      const configs = await getSavedConnectionConfigs();
      setSavedConfigs(configs);
    } catch (error) {
      console.error('Failed to load saved configs:', error);
    }
  };

  const handleTestConnection = async (config: SSHConnectionConfig) => {
    setTestingConnections(prev => new Set(prev).add(config.id));
    
    try {
      const isConnected = await testConnection(config);
      
      if (isConnected) {
        toast.success(`Connection to ${config.name} successful!`);
      } else {
        toast.error(`Connection to ${config.name} failed`);
      }
    } catch (error) {
      toast.error(`Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setTestingConnections(prev => {
        const next = new Set(prev);
        next.delete(config.id);
        return next;
      });
    }
  };

  const handleConnect = async (config: SSHConnectionConfig) => {
    setConnectingIds(prev => new Set(prev).add(config.id));
    
    try {
      await connect(config);
      toast.success(`Connected to ${config.name}`);
      setShowAddDialog(false);
      setShowEditDialog(false);
    } catch (error) {
      toast.error(`Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setConnectingIds(prev => {
        const next = new Set(prev);
        next.delete(config.id);
        return next;
      });
    }
  };

  const handleDisconnect = async (connectionId: string) => {
    try {
      await disconnect(connectionId);
      toast.success('Disconnected successfully');
    } catch (error) {
      toast.error(`Disconnect failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleSaveConfig = async (config: SSHConnectionConfig) => {
    try {
      await saveConnectionConfig(config);
      await loadSavedConfigs();
      toast.success('Connection configuration saved');
    } catch (error) {
      toast.error(`Save failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleDeleteConfig = async (connectionId: string) => {
    try {
      await deleteConnectionConfig(connectionId);
      await loadSavedConfigs();
      toast.success('Connection configuration deleted');
    } catch (error) {
      toast.error(`Delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const getStatusIcon = (status: SSHConnectionStatus) => {
    if (status.connected) {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    return <XCircle className="h-4 w-4 text-red-600" />;
  };

  const getStatusColor = (status: SSHConnectionStatus) => {
    if (status.connected) {
      return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
    }
    return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-semibold">SSH Docker Connections</h2>
          <p className="text-muted-foreground">
            Manage Docker containers on remote servers via SSH
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={refreshConnections} disabled={loading}>
            {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Refresh'}
          </Button>
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Connection
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <ConnectionForm
                onSave={handleSaveConfig}
                onConnect={handleConnect}
                onTest={handleTestConnection}
                onCancel={() => setShowAddDialog(false)}
                testing={testingConnections}
                connecting={connectingIds}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-4 rounded-lg bg-red-50 border border-red-200 text-red-700">
          {error}
        </div>
      )}

      <Tabs defaultValue="active" className="w-full">
        <TabsList>
          <TabsTrigger value="active">Active Connections</TabsTrigger>
          <TabsTrigger value="saved">Saved Configurations</TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <AnimatePresence mode="popLayout">
              {connections.map((status) => (
                <motion.div
                  key={status.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  layout
                >
                  <Card 
                    className={cn(
                      'cursor-pointer transition-all hover:shadow-md',
                      selectedConnectionId === status.id && 'ring-2 ring-blue-500'
                    )}
                    onClick={() => onConnectionSelect?.(status.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(status)}
                          <CardTitle className="text-lg">{status.id}</CardTitle>
                        </div>
                        <Badge className={cn('text-xs', getStatusColor(status))}>
                          {status.connected ? 'Connected' : 'Disconnected'}
                        </Badge>
                      </div>
                      {status.error && (
                        <CardDescription className="text-red-600">
                          {status.error}
                        </CardDescription>
                      )}
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        {status.dockerAvailable && (
                          <div className="flex items-center gap-2">
                            <HardDrive className="h-4 w-4" />
                            <span>Docker {status.dockerVersion}</span>
                          </div>
                        )}
                        {status.lastConnected && (
                          <div className="text-muted-foreground">
                            Last connected: {status.lastConnected.toLocaleString()}
                          </div>
                        )}
                      </div>
                      <div className="flex gap-2 mt-4">
                        {status.connected ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDisconnect(status.id);
                            }}
                          >
                            <WifiOff className="h-4 w-4 mr-1" />
                            Disconnect
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Would need the config to reconnect
                            }}
                            disabled
                          >
                            <Wifi className="h-4 w-4 mr-1" />
                            Reconnect
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {connections.length === 0 && !loading && (
            <div className="text-center py-12">
              <Server className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">No active connections</h3>
              <p className="text-muted-foreground mb-4">
                Create your first SSH connection to start managing remote Docker containers
              </p>
              <Button onClick={() => setShowAddDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Connection
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="saved">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {savedConfigs.map((config) => (
              <Card key={config.id}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{config.name}</CardTitle>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setEditingConnection(config);
                          setShowEditDialog(true);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Configuration</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete this SSH connection configuration?
                              This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDeleteConfig(config.id)}>
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                  <CardDescription>
                    {config.username}@{config.host}:{config.port}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTestConnection(config)}
                      disabled={testingConnections.has(config.id)}
                    >
                      {testingConnections.has(config.id) ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-1" />
                      ) : (
                        <TestTube className="h-4 w-4 mr-1" />
                      )}
                      Test
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleConnect(config)}
                      disabled={connectingIds.has(config.id)}
                    >
                      {connectingIds.has(config.id) ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-1" />
                      ) : (
                        <Wifi className="h-4 w-4 mr-1" />
                      )}
                      Connect
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {savedConfigs.length === 0 && (
            <div className="text-center py-12">
              <Settings className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">No saved configurations</h3>
              <p className="text-muted-foreground">
                Save connection configurations for quick access
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl">
          {editingConnection && (
            <ConnectionForm
              initialConfig={editingConnection}
              onSave={handleSaveConfig}
              onConnect={handleConnect}
              onTest={handleTestConnection}
              onCancel={() => {
                setShowEditDialog(false);
                setEditingConnection(null);
              }}
              testing={testingConnections}
              connecting={connectingIds}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Connection Form Component
interface ConnectionFormProps {
  initialConfig?: SSHConnectionConfig;
  onSave: (config: SSHConnectionConfig) => void;
  onConnect: (config: SSHConnectionConfig) => void;
  onTest: (config: SSHConnectionConfig) => void;
  onCancel: () => void;
  testing: Set<string>;
  connecting: Set<string>;
}

function ConnectionForm({
  initialConfig,
  onSave,
  onConnect,
  onTest,
  onCancel,
  testing,
  connecting,
}: ConnectionFormProps) {
  const [config, setConfig] = useState<SSHConnectionConfig>(
    initialConfig || {
      id: `ssh-${Date.now()}`,
      name: '',
      host: '',
      port: 22,
      username: '',
      authentication: {
        type: 'password',
      },
      options: {
        connectTimeout: 15000,
        keepaliveInterval: 30000,
        keepaliveCountMax: 3,
      },
    }
  );

  const [showPassword, setShowPassword] = useState(false);
  const [showPrivateKey, setShowPrivateKey] = useState(false);

  const updateConfig = (updates: Partial<SSHConnectionConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const updateAuth = (updates: Partial<SSHConnectionConfig['authentication']>) => {
    setConfig(prev => ({
      ...prev,
      authentication: { ...prev.authentication, ...updates }
    }));
  };

  const isValid = config.name && config.host && config.username && 
    (config.authentication.type !== 'password' || config.authentication.password) &&
    (config.authentication.type !== 'privateKey' || config.authentication.privateKey);

  return (
    <>
      <DialogHeader>
        <DialogTitle>{initialConfig ? 'Edit' : 'Add'} SSH Connection</DialogTitle>
        <DialogDescription>
          Configure SSH connection to manage Docker on remote servers
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-4 py-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">Connection Name</Label>
            <Input
              id="name"
              value={config.name}
              onChange={(e) => updateConfig({ name: e.target.value })}
              placeholder="My Docker Server"
            />
          </div>
          <div>
            <Label htmlFor="host">Host</Label>
            <Input
              id="host"
              value={config.host}
              onChange={(e) => updateConfig({ host: e.target.value })}
              placeholder="*************"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="port">Port</Label>
            <Input
              id="port"
              type="number"
              value={config.port}
              onChange={(e) => updateConfig({ port: parseInt(e.target.value) || 22 })}
            />
          </div>
          <div>
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              value={config.username}
              onChange={(e) => updateConfig({ username: e.target.value })}
              placeholder="root"
            />
          </div>
        </div>

        <Separator />

        <div>
          <Label>Authentication Method</Label>
          <Select
            value={config.authentication.type}
            onValueChange={(value: 'password' | 'privateKey' | 'agent') => 
              updateAuth({ type: value })
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="password">
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  Password
                </div>
              </SelectItem>
              <SelectItem value="privateKey">
                <div className="flex items-center gap-2">
                  <Key className="h-4 w-4" />
                  Private Key
                </div>
              </SelectItem>
              <SelectItem value="agent">
                <div className="flex items-center gap-2">
                  <Terminal className="h-4 w-4" />
                  SSH Agent
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {config.authentication.type === 'password' && (
          <div>
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={config.authentication.password || ''}
                onChange={(e) => updateAuth({ password: e.target.value })}
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-2 top-1/2 -translate-y-1/2"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        )}

        {config.authentication.type === 'privateKey' && (
          <div className="space-y-3">
            <div>
              <Label htmlFor="privateKey">Private Key Path</Label>
              <Input
                id="privateKey"
                value={config.authentication.privateKey || ''}
                onChange={(e) => updateAuth({ privateKey: e.target.value })}
                placeholder="/home/<USER>/.ssh/id_rsa"
              />
            </div>
            <div>
              <Label htmlFor="passphrase">Passphrase (if encrypted)</Label>
              <div className="relative">
                <Input
                  id="passphrase"
                  type={showPrivateKey ? 'text' : 'password'}
                  value={config.authentication.passphrase || ''}
                  onChange={(e) => updateAuth({ passphrase: e.target.value })}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2"
                  onClick={() => setShowPrivateKey(!showPrivateKey)}
                >
                  {showPrivateKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </div>
        )}

        {config.authentication.type === 'agent' && (
          <div>
            <Label htmlFor="agentPath">SSH Agent Path (optional)</Label>
            <Input
              id="agentPath"
              value={config.authentication.agentPath || ''}
              onChange={(e) => updateAuth({ agentPath: e.target.value })}
              placeholder="Leave empty for default"
            />
          </div>
        )}
      </div>

      <DialogFooter>
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          variant="outline"
          onClick={() => onTest(config)}
          disabled={!isValid || testing.has(config.id)}
        >
          {testing.has(config.id) ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <TestTube className="h-4 w-4 mr-2" />
          )}
          Test Connection
        </Button>
        <Button onClick={() => onSave(config)} disabled={!isValid}>
          <Save className="h-4 w-4 mr-2" />
          Save
        </Button>
        <Button
          onClick={() => onConnect(config)}
          disabled={!isValid || connecting.has(config.id)}
        >
          {connecting.has(config.id) ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <Wifi className="h-4 w-4 mr-2" />
          )}
          Connect
        </Button>
      </DialogFooter>
    </>
  );
}