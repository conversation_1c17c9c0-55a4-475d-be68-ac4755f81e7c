'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Code, 
  Braces, 
  Variable, 
  Package, 
  FileCode2,
  ChevronDown,
  ChevronUp,
  Zap,
  Check,
} from 'lucide-react';

import { AICompletionSuggestion } from '@/types/ai-code-editor';

interface CodeCompletionPopupProps {
  suggestions: AICompletionSuggestion[];
  position: { x: number; y: number };
  onApply: (suggestionId: string) => void;
  onDismiss: () => void;
  className?: string;
}

const SUGGESTION_TYPE_ICONS = {
  completion: Code,
  snippet: FileCode2,
  import: Package,
  function: Braces,
  variable: Variable,
};

const SUGGESTION_TYPE_COLORS = {
  completion: 'bg-blue-500/10 text-blue-600 border-blue-200',
  snippet: 'bg-purple-500/10 text-purple-600 border-purple-200',
  import: 'bg-green-500/10 text-green-600 border-green-200',
  function: 'bg-orange-500/10 text-orange-600 border-orange-200',
  variable: 'bg-cyan-500/10 text-cyan-600 border-cyan-200',
};

export function CodeCompletionPopup({
  suggestions,
  position,
  onApply,
  onDismiss,
  className,
}: CodeCompletionPopupProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [showDetails, setShowDetails] = useState(false);

  // Reset selection when suggestions change
  useEffect(() => {
    setSelectedIndex(0);
  }, [suggestions]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => Math.min(prev + 1, suggestions.length - 1));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => Math.max(prev - 1, 0));
          break;
        case 'Enter':
        case 'Tab':
          e.preventDefault();
          if (suggestions[selectedIndex]) {
            onApply(suggestions[selectedIndex].id);
          }
          break;
        case 'Escape':
          e.preventDefault();
          onDismiss();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [suggestions, selectedIndex, onApply, onDismiss]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as Element;
      if (!target.closest('[data-completion-popup]')) {
        onDismiss();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onDismiss]);

  const handleSuggestionClick = useCallback((suggestionId: string) => {
    onApply(suggestionId);
  }, [onApply]);

  const selectedSuggestion = suggestions[selectedIndex];

  if (suggestions.length === 0) {
    return null;
  }

  return (
    <motion.div
      data-completion-popup
      initial={{ opacity: 0, y: -10, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -10, scale: 0.95 }}
      transition={{ duration: 0.15, ease: 'easeOut' }}
      className={cn("absolute z-50", className)}
      style={{
        left: position.x,
        top: position.y,
        maxWidth: '400px',
        minWidth: '300px',
      }}
    >
      <Card className="shadow-lg border-2 backdrop-blur-md bg-background/95">
        <CardContent className="p-0">
          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b bg-muted/50">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">AI Suggestions</span>
              <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                {suggestions.length}
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="h-6 w-6 p-0"
            >
              {showDetails ? (
                <ChevronUp className="h-3 w-3" />
              ) : (
                <ChevronDown className="h-3 w-3" />
              )}
            </Button>
          </div>

          {/* Suggestions List */}
          <div className="max-h-64 overflow-y-auto">
            {suggestions.map((suggestion, index) => {
              const Icon = SUGGESTION_TYPE_ICONS[suggestion.type];
              const isSelected = index === selectedIndex;
              
              return (
                <motion.div
                  key={suggestion.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.02 }}
                  className={cn(
                    "flex items-start gap-3 p-3 cursor-pointer transition-colors border-l-2 border-transparent",
                    isSelected && "bg-primary/5 border-l-primary",
                    !isSelected && "hover:bg-muted/50"
                  )}
                  onClick={() => handleSuggestionClick(suggestion.id)}
                >
                  <div className={cn(
                    "flex-shrink-0 p-1.5 rounded-md border",
                    SUGGESTION_TYPE_COLORS[suggestion.type]
                  )}>
                    <Icon className="h-3 w-3" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <code className="text-sm font-mono font-medium truncate">
                        {suggestion.text}
                      </code>
                      <div className="flex items-center gap-1">
                        <Badge
                          variant="outline"
                          className="h-4 px-1 text-xs capitalize"
                        >
                          {suggestion.type}
                        </Badge>
                        <div className="flex items-center gap-1">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <div
                              key={i}
                              className={cn(
                                "w-1 h-1 rounded-full",
                                i < Math.round(suggestion.confidence * 5)
                                  ? "bg-primary"
                                  : "bg-muted"
                              )}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    {suggestion.description && (
                      <p className="text-xs text-muted-foreground line-clamp-2">
                        {suggestion.description}
                      </p>
                    )}
                  </div>
                  
                  {isSelected && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="flex-shrink-0 p-1 rounded-full bg-primary text-primary-foreground"
                    >
                      <Check className="h-3 w-3" />
                    </motion.div>
                  )}
                </motion.div>
              );
            })}
          </div>

          {/* Details Panel */}
          <AnimatePresence>
            {showDetails && selectedSuggestion && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="border-t bg-muted/25"
              >
                <div className="p-3">
                  <div className="mb-2">
                    <span className="text-xs font-medium text-muted-foreground">
                      Preview:
                    </span>
                  </div>
                  <div className="bg-background rounded-md p-2 border">
                    <code className="text-xs font-mono whitespace-pre-wrap">
                      {selectedSuggestion.insertText}
                    </code>
                  </div>
                  
                  {selectedSuggestion.documentation && (
                    <div className="mt-2">
                      <span className="text-xs font-medium text-muted-foreground">
                        Documentation:
                      </span>
                      <p className="text-xs text-muted-foreground mt-1">
                        {selectedSuggestion.documentation}
                      </p>
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between mt-2 pt-2 border-t">
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">
                        Confidence:
                      </span>
                      <Badge variant="outline" className="h-4 px-1 text-xs">
                        {Math.round(selectedSuggestion.confidence * 100)}%
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">
                        Priority:
                      </span>
                      <Badge variant="outline" className="h-4 px-1 text-xs">
                        {selectedSuggestion.priority}/10
                      </Badge>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Footer */}
          <div className="flex items-center justify-between p-2 border-t bg-muted/25 text-xs text-muted-foreground">
            <span>↑↓ Navigate • Enter/Tab Apply • Esc Dismiss</span>
            <span>{selectedIndex + 1} of {suggestions.length}</span>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
