'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { 
  Loader2, 
  Shield, 
  AlertCircle, 
  Lock,
  ArrowRight
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
  requireEmailVerification?: boolean;
  requiredRole?: string | string[];
  allowedRoles?: string[];
  loadingComponent?: React.ReactNode;
  adminOnly?: boolean;
}

export function ProtectedRoute({
  children,
  fallback,
  redirectTo,
  requireEmailVerification = false,
  requiredRole,
  allowedRoles,
  loadingComponent,
  adminOnly = false,
}: ProtectedRouteProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, profile, isLoading, isAuthenticated, checkSession } = useAuth();
  const [isChecking, setIsChecking] = useState(true);
  const [hasTimedOut, setHasTimedOut] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      if (!isLoading) {
        try {
          await checkSession();
        } catch (error) {
          console.error('Auth check failed:', error);
        } finally {
          setIsChecking(false);
        }
      }
    };

    checkAuth();

    // Set a timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      console.warn('Authentication check timed out');
      setHasTimedOut(true);
      setIsChecking(false);
    }, 10000); // 10 second timeout

    return () => clearTimeout(timeout);
  }, [isLoading, checkSession]);

  // Show loading state
  if ((isLoading || isChecking) && !hasTimedOut) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="flex flex-col items-center justify-center p-8 space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <div className="text-center space-y-2">
              <h3 className="font-semibold">Checking authentication...</h3>
              <p className="text-sm text-muted-foreground">
                Please wait while we verify your session.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Handle timeout case
  if (hasTimedOut && !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="flex flex-col items-center justify-center p-8 space-y-4">
            <AlertCircle className="h-8 w-8 text-yellow-500" />
            <div className="text-center space-y-2">
              <h3 className="font-semibold">Authentication Timeout</h3>
              <p className="text-sm text-muted-foreground">
                Authentication check took too long. Please try logging in.
              </p>
            </div>
            <Button
              onClick={() => router.push('/auth/login')}
              className="w-full"
            >
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Not authenticated
  if (!isAuthenticated || !user) {
    if (fallback) {
      return <>{fallback}</>;
    }

    // Redirect to login with return URL
    const loginUrl = redirectTo || '/auth/login';
    const returnUrl = `${loginUrl}?redirect=${encodeURIComponent(pathname)}`;
    
    router.push(returnUrl);
    
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="flex flex-col items-center justify-center p-8 space-y-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
              <Lock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-semibold">Authentication Required</h3>
              <p className="text-sm text-muted-foreground">
                You need to sign in to access this page.
              </p>
            </div>
            <Button onClick={() => router.push(returnUrl)} className="w-full">
              Sign In
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Helper function to check if user has required role
  const hasRequiredRole = () => {
    if (!profile) return false;

    const userRole = profile.role || 'user';

    // Check admin-only access
    if (adminOnly && userRole !== 'admin') {
      return false;
    }

    // Check specific required role
    if (requiredRole) {
      if (Array.isArray(requiredRole)) {
        return requiredRole.includes(userRole);
      }
      return userRole === requiredRole;
    }

    // Check allowed roles
    if (allowedRoles && allowedRoles.length > 0) {
      return allowedRoles.includes(userRole);
    }

    return true;
  };

  // Check email verification requirement
  if (requireEmailVerification && !user.emailVerification) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="flex flex-col items-center justify-center p-8 space-y-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/20">
              <AlertCircle className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-semibold">Email Verification Required</h3>
              <p className="text-sm text-muted-foreground">
                Please verify your email address to access this page.
              </p>
            </div>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Check your email for a verification link, or request a new one.
              </AlertDescription>
            </Alert>
            <div className="flex flex-col w-full space-y-2">
              <Button onClick={() => router.push('/auth/verify-email')} className="w-full">
                Verify Email
              </Button>
              <Button 
                variant="outline" 
                onClick={() => router.push('/dashboard')} 
                className="w-full"
              >
                Continue to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check role requirement
  if (!hasRequiredRole()) {
    const userRole = profile?.role || 'user';
    const requiredRoleText = adminOnly ? 'admin' :
      Array.isArray(requiredRole) ? requiredRole.join(' or ') :
      requiredRole || allowedRoles?.join(' or ') || 'authorized';

    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="flex flex-col items-center justify-center p-8 space-y-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
              <Shield className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-semibold">Access Denied</h3>
              <p className="text-sm text-muted-foreground">
                You don't have the required permissions to access this page.
              </p>
            </div>
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This page requires {requiredRoleText} role.
                Your current role: {userRole}
              </AlertDescription>
            </Alert>
            <div className="flex flex-col w-full space-y-2">
              <Button onClick={() => router.push('/pricing')} className="w-full">
                Upgrade Plan
              </Button>
              <Button 
                variant="outline" 
                onClick={() => router.push('/dashboard')} 
                className="w-full"
              >
                Back to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // All checks passed, render children
  return <>{children}</>;
}

// Higher-order component for protecting pages
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<ProtectedRouteProps, 'children'>
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

// Hook for checking authentication status in components
export function useRequireAuth(options?: {
  redirectTo?: string;
  requireEmailVerification?: boolean;
  requiredRole?: string;
}) {
  const { user, profile, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      const loginUrl = options?.redirectTo || '/auth/login';
      const returnUrl = `${loginUrl}?redirect=${encodeURIComponent(pathname)}`;
      router.push(returnUrl);
      return;
    }

    if (
      options?.requireEmailVerification && 
      user && 
      !user.emailVerification
    ) {
      router.push('/auth/verify-email');
      return;
    }

    if (
      options?.requiredRole && 
      profile?.subscription?.plan !== options.requiredRole
    ) {
      router.push('/pricing');
      return;
    }
  }, [
    isLoading, 
    isAuthenticated, 
    user, 
    profile, 
    router, 
    pathname, 
    options
  ]);

  return {
    user,
    profile,
    isLoading,
    isAuthenticated,
    isAuthorized: isAuthenticated && 
      (!options?.requireEmailVerification || user?.emailVerification) &&
      (!options?.requiredRole || profile?.subscription?.plan === options.requiredRole)
  };
}
