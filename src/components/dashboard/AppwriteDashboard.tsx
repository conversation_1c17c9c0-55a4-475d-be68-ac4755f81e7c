'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/auth-context';
import { useAppwriteDatabase } from '@/hooks/useAppwriteDatabase';
import { useAppwriteStorage } from '@/hooks/useAppwriteStorage';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FileUpload } from '@/components/storage/FileUpload';
import { DocumentManager } from '@/components/database/DocumentManager';
import { 
  Database, 
  HardDrive, 
  Zap, 
  Shield, 
  Activity,
  Users,
  FileText,
  Upload,
  Settings,
  LogOut,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface AppwriteDashboardProps {
  className?: string;
}

interface HealthStatus {
  overall: boolean;
  services: {
    auth: boolean;
    database: boolean;
    storage: boolean;
    functions: boolean;
  };
  timestamp: string;
}

export function AppwriteDashboard({ className = '' }: AppwriteDashboardProps) {
  const auth = useAuth();
  const database = useAppwriteDatabase();
  const storage = useAppwriteStorage();

  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [isLoadingHealth, setIsLoadingHealth] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Check health status
  const checkHealth = async () => {
    try {
      setIsLoadingHealth(true);
      const response = await fetch('/api/appwrite/health');
      const data = await response.json();
      
      if (data.success) {
        setHealthStatus(data.data);
      }
    } catch (error) {
      console.error('Failed to check health:', error);
    } finally {
      setIsLoadingHealth(false);
    }
  };

  useEffect(() => {
    checkHealth();
    // Check health every 30 seconds
    const interval = setInterval(checkHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleLogout = async () => {
    try {
      await existingAuth.logout();
      await serverAuth.logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const { user, isAuthenticated } = auth;

  if (!isAuthenticated || !user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Shield className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Authentication Required</h3>
            <p className="text-muted-foreground text-center">
              Please sign in to access the Appwrite dashboard.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 ${className}`}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Appwrite Dashboard
            </h1>
            <p className="text-muted-foreground mt-1">
              Welcome back, {user.name || user.email}
            </p>
          </div>

          <div className="flex items-center gap-4">
            {/* Health Status */}
            <div className="flex items-center gap-2">
              {isLoadingHealth ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : healthStatus ? (
                <>
                  {healthStatus.overall ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                  <Badge variant={healthStatus.overall ? 'default' : 'destructive'}>
                    {healthStatus.overall ? 'Healthy' : 'Issues'}
                  </Badge>
                </>
              ) : (
                <Badge variant="outline">Unknown</Badge>
              )}
            </div>

            <Button variant="outline" onClick={handleLogout}>
              <LogOut className="mr-2 h-4 w-4" />
              Logout
            </Button>
          </div>
        </motion.div>

        {/* Service Status Cards */}
        {healthStatus && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8"
          >
            {Object.entries(healthStatus.services).map(([service, status]) => {
              const icons = {
                auth: Shield,
                database: Database,
                storage: HardDrive,
                functions: Zap,
              };
              const Icon = icons[service as keyof typeof icons];

              return (
                <Card key={service}>
                  <CardContent className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${status ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium capitalize">{service}</p>
                        <p className="text-xs text-muted-foreground">
                          {status ? 'Operational' : 'Issues'}
                        </p>
                      </div>
                    </div>
                    <Badge variant={status ? 'default' : 'destructive'} className="text-xs">
                      {status ? 'OK' : 'Error'}
                    </Badge>
                  </CardContent>
                </Card>
              );
            })}
          </motion.div>
        )}

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="database" className="flex items-center gap-2">
                <Database className="h-4 w-4" />
                Database
              </TabsTrigger>
              <TabsTrigger value="storage" className="flex items-center gap-2">
                <HardDrive className="h-4 w-4" />
                Storage
              </TabsTrigger>
              <TabsTrigger value="functions" className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Functions
              </TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      User Profile
                    </CardTitle>
                    <CardDescription>Your account information</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Name</p>
                      <p className="text-sm text-muted-foreground">{user.name || 'Not set'}</p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Email</p>
                      <p className="text-sm text-muted-foreground">{user.email}</p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm font-medium">User ID</p>
                      <p className="text-sm text-muted-foreground font-mono">{user.$id}</p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Account Created</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(user.$createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Quick Actions
                    </CardTitle>
                    <CardDescription>Common tasks and operations</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                      onClick={() => setActiveTab('database')}
                    >
                      <Database className="mr-2 h-4 w-4" />
                      Manage Documents
                    </Button>
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                      onClick={() => setActiveTab('storage')}
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Files
                    </Button>
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                      onClick={() => setActiveTab('functions')}
                    >
                      <Zap className="mr-2 h-4 w-4" />
                      Execute Functions
                    </Button>
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                      onClick={checkHealth}
                      disabled={isLoadingHealth}
                    >
                      {isLoadingHealth ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Activity className="mr-2 h-4 w-4" />
                      )}
                      Refresh Health Status
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Database Tab */}
            <TabsContent value="database" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Database Management
                  </CardTitle>
                  <CardDescription>
                    Manage your Appwrite database collections and documents
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {database.error && (
                    <Alert variant="destructive" className="mb-4">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{database.error}</AlertDescription>
                    </Alert>
                  )}
                  
                  <DocumentManager
                    databaseId={process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || 'default'}
                    collectionId={process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID || 'users'}
                    title="User Documents"
                    description="Manage user-related documents in your collection"
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Storage Tab */}
            <TabsContent value="storage" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <HardDrive className="h-5 w-5" />
                    File Storage
                  </CardTitle>
                  <CardDescription>
                    Upload and manage files in your Appwrite storage
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {storage.error && (
                    <Alert variant="destructive" className="mb-4">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{storage.error}</AlertDescription>
                    </Alert>
                  )}
                  
                  <FileUpload
                    bucketId={process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID || 'default'}
                    onUploadComplete={(files) => {
                      console.log('Files uploaded:', files);
                    }}
                    onUploadError={(error) => {
                      console.error('Upload error:', error);
                    }}
                    maxFiles={5}
                    maxSize={10 * 1024 * 1024} // 10MB
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Functions Tab */}
            <TabsContent value="functions" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    Serverless Functions
                  </CardTitle>
                  <CardDescription>
                    Execute and manage your Appwrite functions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Functions Management</h3>
                    <p className="text-muted-foreground mb-4">
                      Function execution and management interface coming soon.
                    </p>
                    <Button variant="outline">
                      <Settings className="mr-2 h-4 w-4" />
                      Configure Functions
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </div>
  );
}
