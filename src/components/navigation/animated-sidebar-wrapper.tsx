'use client';

import React from 'react';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { cn } from '@/lib/utils';
import { SidebarAnimationConfig, SidebarThemeConfig } from '@/types/navigation';

// Default animation configuration
const defaultAnimationConfig: SidebarAnimationConfig = {
  enabled: true,
  duration: 0.3,
  easing: 'easeInOut',
  stagger: 0.05,
  hover: {
    scale: 1.02,
    duration: 0.2,
  },
  collapse: {
    duration: 0.25,
    easing: 'easeInOut',
  },
};

// Default theme configuration with glassmorphism
const defaultThemeConfig: SidebarThemeConfig = {
  glassmorphism: {
    enabled: true,
    opacity: 0.8,
    blur: 12,
  },
  colors: {
    background: 'hsl(var(--sidebar))',
    foreground: 'hsl(var(--sidebar-foreground))',
    accent: 'hsl(var(--sidebar-accent))',
    border: 'hsl(var(--sidebar-border))',
    hover: 'hsl(var(--sidebar-accent) / 0.5)',
    active: 'hsl(var(--sidebar-primary))',
  },
  spacing: {
    padding: '1rem',
    margin: '0.5rem',
    gap: '0.5rem',
  },
  typography: {
    fontSize: '0.875rem',
    fontWeight: '500',
    lineHeight: '1.25rem',
  },
};

interface AnimatedSidebarWrapperProps {
  children: React.ReactNode;
  animationConfig?: Partial<SidebarAnimationConfig>;
  themeConfig?: Partial<SidebarThemeConfig>;
  variant?: 'main' | 'page' | 'overlay';
  isCollapsed?: boolean;
  isVisible?: boolean;
  className?: string;
}

// Animation variants for different sidebar types
const sidebarVariants: Record<string, Variants> = {
  main: {
    expanded: {
      width: 280,
      opacity: 1,
      transition: {
        duration: 0.3,
        ease: 'easeInOut',
        staggerChildren: 0.05,
      },
    },
    collapsed: {
      width: 60,
      opacity: 1,
      transition: {
        duration: 0.25,
        ease: 'easeInOut',
        staggerChildren: 0.02,
      },
    },
    hidden: {
      width: 0,
      opacity: 0,
      transition: {
        duration: 0.2,
        ease: 'easeInOut',
      },
    },
  },
  page: {
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
        staggerChildren: 0.05,
      },
    },
    hidden: {
      x: '100%',
      opacity: 0,
      transition: {
        duration: 0.2,
        ease: 'easeInOut',
      },
    },
  },
  overlay: {
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 40,
      },
    },
    hidden: {
      x: '-100%',
      opacity: 0,
      transition: {
        duration: 0.3,
        ease: 'easeInOut',
      },
    },
  },
};

// Item animation variants
const itemVariants: Variants = {
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.2,
      ease: 'easeOut',
    },
  },
  hidden: {
    opacity: 0,
    y: 10,
    scale: 0.95,
    transition: {
      duration: 0.15,
      ease: 'easeIn',
    },
  },
  hover: {
    scale: 1.02,
    y: -1,
    transition: {
      duration: 0.15,
      ease: 'easeOut',
    },
  },
  tap: {
    scale: 0.98,
    transition: {
      duration: 0.1,
      ease: 'easeInOut',
    },
  },
};

export function AnimatedSidebarWrapper({
  children,
  animationConfig = {},
  themeConfig = {},
  variant = 'main',
  isCollapsed = false,
  isVisible = true,
  className,
}: AnimatedSidebarWrapperProps) {
  const config = { ...defaultAnimationConfig, ...animationConfig };
  const theme = { ...defaultThemeConfig, ...themeConfig };

  // Determine animation state
  const getAnimationState = () => {
    if (!isVisible) return 'hidden';
    if (variant === 'main') {
      return isCollapsed ? 'collapsed' : 'expanded';
    }
    return 'visible';
  };

  // Generate glassmorphism styles
  const glassmorphismStyles = theme.glassmorphism.enabled
    ? {
        backdropFilter: `blur(${theme.glassmorphism.blur}px)`,
        backgroundColor: `${theme.colors.background}/${theme.glassmorphism.opacity}`,
        borderColor: theme.colors.border,
      }
    : {};

  return (
    <motion.div
      variants={config.enabled ? sidebarVariants[variant] : undefined}
      initial="hidden"
      animate={getAnimationState()}
      exit="hidden"
      className={cn(
        'relative overflow-hidden',
        theme.glassmorphism.enabled && 'border backdrop-blur-md',
        className
      )}
      style={{
        ...glassmorphismStyles,
        '--sidebar-padding': theme.spacing.padding,
        '--sidebar-margin': theme.spacing.margin,
        '--sidebar-gap': theme.spacing.gap,
        '--sidebar-font-size': theme.typography.fontSize,
        '--sidebar-font-weight': theme.typography.fontWeight,
        '--sidebar-line-height': theme.typography.lineHeight,
      } as React.CSSProperties}
    >
      {/* Glassmorphism overlay */}
      {theme.glassmorphism.enabled && (
        <div
          className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none"
          style={{
            background: `linear-gradient(135deg, ${theme.colors.background}/0.1 0%, transparent 100%)`,
          }}
        />
      )}

      {/* Content wrapper with stagger animation */}
      <motion.div
        variants={{
          expanded: {
            transition: {
              staggerChildren: config.stagger,
              delayChildren: 0.1,
            },
          },
          collapsed: {
            transition: {
              staggerChildren: config.stagger / 2,
            },
          },
          visible: {
            transition: {
              staggerChildren: config.stagger,
              delayChildren: 0.05,
            },
          },
        }}
        className="relative z-10 h-full"
      >
        {children}
      </motion.div>
    </motion.div>
  );
}

// Animated sidebar item wrapper
interface AnimatedSidebarItemProps {
  children: React.ReactNode;
  delay?: number;
  isActive?: boolean;
  isDisabled?: boolean;
  onClick?: () => void;
  className?: string;
}

export function AnimatedSidebarItem({
  children,
  delay = 0,
  isActive = false,
  isDisabled = false,
  onClick,
  className,
}: AnimatedSidebarItemProps) {
  return (
    <motion.div
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      exit="hidden"
      whileHover={!isDisabled ? 'hover' : undefined}
      whileTap={!isDisabled ? 'tap' : undefined}
      transition={{
        delay,
        duration: 0.2,
        ease: 'easeOut',
      }}
      onClick={!isDisabled ? onClick : undefined}
      className={cn(
        'relative cursor-pointer transition-colors duration-200',
        isActive && 'bg-sidebar-accent text-sidebar-accent-foreground',
        isDisabled && 'opacity-50 cursor-not-allowed',
        !isDisabled && 'hover:bg-sidebar-accent/50',
        className
      )}
    >
      {/* Active indicator */}
      {isActive && (
        <motion.div
          layoutId="activeIndicator"
          className="absolute left-0 top-0 bottom-0 w-1 bg-sidebar-primary rounded-r-full"
          transition={{
            type: 'spring',
            stiffness: 400,
            damping: 30,
          }}
        />
      )}

      {/* Hover effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-sidebar-primary/10 to-transparent opacity-0 rounded-md"
        whileHover={{ opacity: 1 }}
        transition={{ duration: 0.2 }}
      />

      {/* Content */}
      <div className="relative z-10">{children}</div>
    </motion.div>
  );
}

// Animated sidebar group wrapper
interface AnimatedSidebarGroupProps {
  children: React.ReactNode;
  title?: string;
  isExpanded?: boolean;
  onToggle?: () => void;
  className?: string;
}

export function AnimatedSidebarGroup({
  children,
  title,
  isExpanded = true,
  onToggle,
  className,
}: AnimatedSidebarGroupProps) {
  return (
    <motion.div
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      className={cn('space-y-2', className)}
    >
      {title && (
        <motion.div
          className="flex items-center justify-between px-3 py-2 text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider cursor-pointer"
          onClick={onToggle}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <span>{title}</span>
          {onToggle && (
            <motion.div
              animate={{ rotate: isExpanded ? 0 : -90 }}
              transition={{ duration: 0.2 }}
            >
              ▼
            </motion.div>
          )}
        </motion.div>
      )}

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{
              duration: 0.3,
              ease: 'easeInOut',
              staggerChildren: 0.05,
            }}
            className="overflow-hidden"
          >
            <motion.div
              variants={{
                visible: {
                  transition: {
                    staggerChildren: 0.05,
                  },
                },
              }}
              className="space-y-1"
            >
              {children}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

// Floating action button with glassmorphism
interface FloatingActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  className?: string;
}

export function FloatingActionButton({
  children,
  onClick,
  position = 'bottom-right',
  className,
}: FloatingActionButtonProps) {
  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
  };

  return (
    <motion.button
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0, opacity: 0 }}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      onClick={onClick}
      className={cn(
        'fixed z-50 p-3 rounded-full shadow-lg backdrop-blur-md',
        'bg-primary/80 text-primary-foreground',
        'border border-white/20',
        'hover:bg-primary/90 transition-colors duration-200',
        positionClasses[position],
        className
      )}
      style={{
        backdropFilter: 'blur(12px)',
      }}
    >
      {children}
    </motion.button>
  );
}
