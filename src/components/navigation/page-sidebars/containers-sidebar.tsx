'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  HardDrive,
  Play,
  Square,
  Pause,
  RotateCcw,
  Trash2,
  Plus,
  Search,
  Filter,
  Terminal,
  FileText,
  Activity,
  Clock,
  Cpu,
  Network,
  MoreVertical,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

import { PageSidebarProps } from '@/types/navigation';
import { cn } from '@/lib/utils';

// Mock container data
const mockContainers = [
  {
    id: 'container-1',
    name: 'nginx-web',
    image: 'nginx:latest',
    status: 'running',
    created: '2 hours ago',
    ports: ['80:8080', '443:8443'],
    cpu: 15,
    memory: 128,
  },
  {
    id: 'container-2',
    name: 'postgres-db',
    image: 'postgres:14',
    status: 'running',
    created: '1 day ago',
    ports: ['5432:5432'],
    cpu: 8,
    memory: 512,
  },
  {
    id: 'container-3',
    name: 'redis-cache',
    image: 'redis:alpine',
    status: 'stopped',
    created: '3 days ago',
    ports: ['6379:6379'],
    cpu: 0,
    memory: 0,
  },
  {
    id: 'container-4',
    name: 'app-backend',
    image: 'node:18-alpine',
    status: 'starting',
    created: '5 mins ago',
    ports: ['3000:3000'],
    cpu: 25,
    memory: 256,
  },
];

interface ContainersSidebarProps extends Omit<PageSidebarProps, 'config'> {
  onCreateContainer?: () => void;
  onContainerAction?: (containerId: string, action: string) => void;
}

export function ContainersSidebar({
  onCreateContainer,
  onContainerAction,
  onQuickAction,
  onFilterChange,
  className,
  ...props
}: ContainersSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [imageFilter, setImageFilter] = useState<string>('all');

  const filteredContainers = mockContainers.filter(container => {
    const matchesSearch = container.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         container.image.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || container.status === statusFilter;
    const matchesImage = imageFilter === 'all' || container.image.includes(imageFilter);
    return matchesSearch && matchesStatus && matchesImage;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return CheckCircle;
      case 'stopped': return XCircle;
      case 'starting': return Loader2;
      case 'stopping': return AlertTriangle;
      default: return XCircle;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'stopped': return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      case 'starting': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'stopping': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const containerStats = {
    total: mockContainers.length,
    running: mockContainers.filter(c => c.status === 'running').length,
    stopped: mockContainers.filter(c => c.status === 'stopped').length,
    starting: mockContainers.filter(c => c.status === 'starting').length,
  };

  return (
    <Sidebar className={cn('w-80 border-l', className)} {...props}>
      <SidebarHeader className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Containers</h2>
          <Button onClick={onCreateContainer} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Create
          </Button>
        </div>

        {/* Container Stats */}
        <div className="grid grid-cols-2 gap-2 mb-4">
          <div className="p-2 rounded-lg border bg-card text-center">
            <div className="text-lg font-semibold text-green-600">{containerStats.running}</div>
            <div className="text-xs text-muted-foreground">Running</div>
          </div>
          <div className="p-2 rounded-lg border bg-card text-center">
            <div className="text-lg font-semibold text-red-600">{containerStats.stopped}</div>
            <div className="text-xs text-muted-foreground">Stopped</div>
          </div>
        </div>

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search containers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Filters */}
        <div className="flex gap-2 mb-4">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="running">Running</SelectItem>
              <SelectItem value="stopped">Stopped</SelectItem>
              <SelectItem value="starting">Starting</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">
            Active Containers ({filteredContainers.length})
          </SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <AnimatePresence mode="popLayout">
              {filteredContainers.map((container, index) => {
                const StatusIcon = getStatusIcon(container.status);
                
                return (
                  <motion.div
                    key={container.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.05 }}
                    className="mb-2"
                  >
                    <SidebarMenuItem>
                      <div className="group relative p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <StatusIcon className={cn(
                              'h-4 w-4',
                              container.status === 'starting' && 'animate-spin'
                            )} />
                            <div>
                              <div className="font-medium text-sm">{container.name}</div>
                              <div className="text-xs text-muted-foreground">{container.image}</div>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <MoreVertical className="h-3 w-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {container.status === 'running' ? (
                                <>
                                  <DropdownMenuItem onClick={() => onContainerAction?.(container.id, 'stop')}>
                                    <Square className="h-4 w-4 mr-2" />
                                    Stop
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => onContainerAction?.(container.id, 'pause')}>
                                    <Pause className="h-4 w-4 mr-2" />
                                    Pause
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => onContainerAction?.(container.id, 'restart')}>
                                    <RotateCcw className="h-4 w-4 mr-2" />
                                    Restart
                                  </DropdownMenuItem>
                                </>
                              ) : (
                                <DropdownMenuItem onClick={() => onContainerAction?.(container.id, 'start')}>
                                  <Play className="h-4 w-4 mr-2" />
                                  Start
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => onContainerAction?.(container.id, 'logs')}>
                                <FileText className="h-4 w-4 mr-2" />
                                View Logs
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => onContainerAction?.(container.id, 'terminal')}>
                                <Terminal className="h-4 w-4 mr-2" />
                                Terminal
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => onContainerAction?.(container.id, 'delete')}
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        <div className="flex items-center gap-2 mb-2">
                          <Badge className={cn('text-xs', getStatusColor(container.status))}>
                            {container.status}
                          </Badge>
                          {container.status === 'running' && (
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <Cpu className="h-3 w-3" />
                                {container.cpu}%
                              </span>
                              <span>{container.memory}MB</span>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {container.created}
                          </span>
                          <div className="flex items-center gap-1">
                            <Network className="h-3 w-3" />
                            <span>{container.ports.length} ports</span>
                          </div>
                        </div>
                      </div>
                    </SidebarMenuItem>
                  </motion.div>
                );
              })}
            </AnimatePresence>

            {filteredContainers.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <HardDrive className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No containers found</p>
                {searchQuery && (
                  <p className="text-xs mt-1">Try adjusting your search or filters</p>
                )}
              </div>
            )}
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="my-4" />

        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Quick Actions</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={() => onQuickAction?.({ id: 'bulk-start', type: 'button', title: 'Start All', onClick: () => {} })}>
                  <Play className="h-4 w-4 mr-2" />
                  Start All Stopped
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={() => onQuickAction?.({ id: 'bulk-stop', type: 'button', title: 'Stop All', onClick: () => {} })}>
                  <Square className="h-4 w-4 mr-2" />
                  Stop All Running
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={() => onQuickAction?.({ id: 'cleanup', type: 'button', title: 'Cleanup', onClick: () => {} })}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Cleanup Unused
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
