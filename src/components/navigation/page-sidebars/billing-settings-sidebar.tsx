'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CreditCard,
  DollarSign,
  Calendar,
  Download,
  Upload,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Settings,
  Plus,
  Edit3,
  Trash2,
  RefreshCw,
  ExternalLink,
  FileText,
  BarChart3,
  Package,
  Users,
  HardDrive,
  Zap,
  Globe,
  Shield,
  Star,
  Crown,
  Gift,
  Bell,
  Info,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

import { SettingsSidebarProps, StatItem } from '@/types/navigation';
import { cn } from '@/lib/utils';

interface Subscription {
  id: string;
  plan: string;
  status: 'active' | 'cancelled' | 'past_due' | 'trialing';
  price: number;
  currency: string;
  billingCycle: 'monthly' | 'yearly';
  nextBillingDate: Date;
  trialEndsAt?: Date;
  features: string[];
  limits: {
    workspaces: number;
    storage: number; // GB
    users: number;
    containers: number;
  };
}

interface PaymentMethod {
  id: string;
  type: 'card' | 'bank' | 'paypal';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
  email?: string; // for PayPal
}

interface Invoice {
  id: string;
  number: string;
  date: Date;
  amount: number;
  currency: string;
  status: 'paid' | 'pending' | 'failed' | 'refunded';
  downloadUrl?: string;
}

interface UsageMetrics {
  workspaces: { used: number; limit: number };
  storage: { used: number; limit: number }; // GB
  users: { used: number; limit: number };
  containers: { used: number; limit: number };
  bandwidth: { used: number; limit: number }; // GB
}

interface BillingSettingsSidebarProps extends SettingsSidebarProps {
  subscription?: Subscription;
  paymentMethods?: PaymentMethod[];
  recentInvoices?: Invoice[];
  usageMetrics?: UsageMetrics;
  onUpgradePlan?: () => void;
  onCancelSubscription?: () => void;
  onAddPaymentMethod?: () => void;
  onUpdatePaymentMethod?: (methodId: string) => void;
  onRemovePaymentMethod?: (methodId: string) => void;
  onDownloadInvoice?: (invoiceId: string) => void;
  onViewAllInvoices?: () => void;
  onContactSupport?: () => void;
}

export function BillingSettingsSidebar({
  subscription,
  paymentMethods = [],
  recentInvoices = [],
  usageMetrics,
  userPermissions,
  onUpgradePlan,
  onCancelSubscription,
  onAddPaymentMethod,
  onUpdatePaymentMethod,
  onRemovePaymentMethod,
  onDownloadInvoice,
  onViewAllInvoices,
  onContactSupport,
  onQuickAction,
  className,
  loading = false,
  ...props
}: BillingSettingsSidebarProps) {
  // Mock data if not provided
  const defaultSubscription: Subscription = {
    id: 'sub-1',
    plan: 'Professional',
    status: 'active',
    price: 49.99,
    currency: 'USD',
    billingCycle: 'monthly',
    nextBillingDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
    features: [
      'Unlimited workspaces',
      '500GB storage',
      'Up to 10 team members',
      'Priority support',
      'Advanced monitoring',
      'Custom integrations',
    ],
    limits: {
      workspaces: -1, // unlimited
      storage: 500,
      users: 10,
      containers: 100,
    },
  };

  const defaultPaymentMethods: PaymentMethod[] = [
    {
      id: 'pm-1',
      type: 'card',
      last4: '4242',
      brand: 'Visa',
      expiryMonth: 12,
      expiryYear: 2025,
      isDefault: true,
    },
    {
      id: 'pm-2',
      type: 'paypal',
      email: '<EMAIL>',
      isDefault: false,
    },
  ];

  const defaultInvoices: Invoice[] = [
    {
      id: 'inv-1',
      number: 'INV-2024-001',
      date: new Date(),
      amount: 49.99,
      currency: 'USD',
      status: 'paid',
      downloadUrl: '/invoices/inv-2024-001.pdf',
    },
    {
      id: 'inv-2',
      number: 'INV-2023-012',
      date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      amount: 49.99,
      currency: 'USD',
      status: 'paid',
      downloadUrl: '/invoices/inv-2023-012.pdf',
    },
    {
      id: 'inv-3',
      number: 'INV-2023-011',
      date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
      amount: 49.99,
      currency: 'USD',
      status: 'paid',
      downloadUrl: '/invoices/inv-2023-011.pdf',
    },
  ];

  const defaultUsage: UsageMetrics = {
    workspaces: { used: 8, limit: -1 },
    storage: { used: 245, limit: 500 },
    users: { used: 6, limit: 10 },
    containers: { used: 24, limit: 100 },
    bandwidth: { used: 125, limit: 1000 },
  };

  const currentSubscription = subscription || defaultSubscription;
  const methods = paymentMethods.length > 0 ? paymentMethods : defaultPaymentMethods;
  const invoices = recentInvoices.length > 0 ? recentInvoices : defaultInvoices;
  const usage = usageMetrics || defaultUsage;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 dark:text-green-400';
      case 'trialing':
        return 'text-blue-600 dark:text-blue-400';
      case 'past_due':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'cancelled':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return CheckCircle;
      case 'trialing':
        return Clock;
      case 'past_due':
        return AlertTriangle;
      case 'cancelled':
        return AlertTriangle;
      default:
        return Info;
    }
  };

  const getPaymentMethodIcon = (type: string) => {
    switch (type) {
      case 'card':
        return CreditCard;
      case 'bank':
        return DollarSign;
      case 'paypal':
        return Globe;
      default:
        return CreditCard;
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  const getUsagePercentage = (used: number, limit: number) => {
    if (limit === -1) return 0; // unlimited
    return Math.min((used / limit) * 100, 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600 dark:text-red-400';
    if (percentage >= 75) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-green-600 dark:text-green-400';
  };

  const daysUntilBilling = Math.ceil(
    (currentSubscription.nextBillingDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24)
  );

  return (
    <Sidebar className={cn('w-80 border-l', className)} {...props}>
      <SidebarHeader className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Billing & Usage
          </h2>
          <Button onClick={onUpgradePlan} size="sm">
            <Crown className="h-4 w-4 mr-2" />
            Upgrade
          </Button>
        </div>

        {/* Current Plan */}
        <div className="p-4 rounded-lg border bg-card">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Package className="h-4 w-4 text-muted-foreground" />
              <span className="font-semibold">{currentSubscription.plan}</span>
            </div>
            <Badge variant={currentSubscription.status === 'active' ? 'default' : 'destructive'}>
              {currentSubscription.status}
            </Badge>
          </div>
          
          <div className="text-2xl font-bold mb-1">
            {formatCurrency(currentSubscription.price)}
            <span className="text-sm font-normal text-muted-foreground">
              /{currentSubscription.billingCycle}
            </span>
          </div>
          
          <div className="text-xs text-muted-foreground">
            Next billing: {formatDate(currentSubscription.nextBillingDate)} ({daysUntilBilling} days)
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        {/* Usage Metrics */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Current Usage</SidebarGroupLabel>
          <SidebarGroupContent className="px-4 space-y-4">
            {/* Workspaces */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Monitor className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Workspaces</span>
                </div>
                <span className="text-sm font-medium">
                  {usage.workspaces.used}
                  {usage.workspaces.limit !== -1 && `/${usage.workspaces.limit}`}
                </span>
              </div>
              {usage.workspaces.limit !== -1 && (
                <Progress 
                  value={getUsagePercentage(usage.workspaces.used, usage.workspaces.limit)} 
                  className="h-2" 
                />
              )}
            </div>

            {/* Storage */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <HardDrive className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Storage</span>
                </div>
                <span className="text-sm font-medium">
                  {usage.storage.used}GB/{usage.storage.limit}GB
                </span>
              </div>
              <Progress 
                value={getUsagePercentage(usage.storage.used, usage.storage.limit)} 
                className="h-2" 
              />
            </div>

            {/* Users */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Team Members</span>
                </div>
                <span className="text-sm font-medium">
                  {usage.users.used}/{usage.users.limit}
                </span>
              </div>
              <Progress 
                value={getUsagePercentage(usage.users.used, usage.users.limit)} 
                className="h-2" 
              />
            </div>

            {/* Containers */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Package className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Containers</span>
                </div>
                <span className="text-sm font-medium">
                  {usage.containers.used}/{usage.containers.limit}
                </span>
              </div>
              <Progress 
                value={getUsagePercentage(usage.containers.used, usage.containers.limit)} 
                className="h-2" 
              />
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Payment Methods */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4 flex items-center justify-between">
            Payment Methods
            <Button
              onClick={onAddPaymentMethod}
              size="sm"
              variant="ghost"
              className="h-6 px-2 text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add
            </Button>
          </SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <div className="space-y-2">
              {methods.map((method, index) => {
                const Icon = getPaymentMethodIcon(method.type);
                
                return (
                  <motion.div
                    key={method.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4 text-muted-foreground" />
                        <div>
                          {method.type === 'card' ? (
                            <div className="text-sm font-medium">
                              {method.brand} •••• {method.last4}
                            </div>
                          ) : method.type === 'paypal' ? (
                            <div className="text-sm font-medium">PayPal</div>
                          ) : (
                            <div className="text-sm font-medium">Bank Account</div>
                          )}
                          {method.type === 'card' && (
                            <div className="text-xs text-muted-foreground">
                              Expires {method.expiryMonth}/{method.expiryYear}
                            </div>
                          )}
                          {method.type === 'paypal' && (
                            <div className="text-xs text-muted-foreground">
                              {method.email}
                            </div>
                          )}
                        </div>
                      </div>
                      {method.isDefault && (
                        <Badge variant="secondary" className="text-xs">Default</Badge>
                      )}
                    </div>
                    
                    <div className="flex gap-1">
                      <Button
                        onClick={() => onUpdatePaymentMethod?.(method.id)}
                        size="sm"
                        variant="outline"
                        className="h-6 px-2 text-xs flex-1"
                      >
                        <Edit3 className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      {!method.isDefault && (
                        <Button
                          onClick={() => onRemovePaymentMethod?.(method.id)}
                          size="sm"
                          variant="outline"
                          className="h-6 px-2 text-xs"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Recent Invoices */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4 flex items-center justify-between">
            Recent Invoices
            <Button
              onClick={onViewAllInvoices}
              size="sm"
              variant="ghost"
              className="h-6 px-2 text-xs"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              View All
            </Button>
          </SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {invoices.slice(0, 5).map((invoice, index) => (
                <motion.div
                  key={invoice.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <div className="text-sm font-medium">{invoice.number}</div>
                      <div className="text-xs text-muted-foreground">
                        {formatDate(invoice.date)}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {formatCurrency(invoice.amount, invoice.currency)}
                      </div>
                      <Badge 
                        variant={invoice.status === 'paid' ? 'default' : 'destructive'}
                        className="text-xs"
                      >
                        {invoice.status}
                      </Badge>
                    </div>
                  </div>
                  
                  {invoice.downloadUrl && (
                    <Button
                      onClick={() => onDownloadInvoice?.(invoice.id)}
                      size="sm"
                      variant="outline"
                      className="w-full h-6 text-xs"
                    >
                      <Download className="h-3 w-3 mr-1" />
                      Download PDF
                    </Button>
                  )}
                </motion.div>
              ))}
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Plan Features */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Plan Features</SidebarGroupLabel>
          <SidebarGroupContent className="px-4">
            <div className="space-y-2">
              {currentSubscription.features.map((feature, index) => (
                <motion.div
                  key={feature}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="flex items-center gap-2 text-sm"
                >
                  <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                  <span>{feature}</span>
                </motion.div>
              ))}
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Quick Actions */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Billing Actions</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={onUpgradePlan}>
                  <Crown className="h-4 w-4 mr-2" />
                  Upgrade Plan
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={() => onQuickAction?.({ 
                  id: 'billing-history', 
                  type: 'button', 
                  title: 'Billing History', 
                  onClick: () => {} 
                })}>
                  <FileText className="h-4 w-4 mr-2" />
                  Billing History
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={() => onQuickAction?.({ 
                  id: 'usage-reports', 
                  type: 'button', 
                  title: 'Usage Reports', 
                  onClick: () => {} 
                })}>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Usage Reports
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={onContactSupport}>
                  <Bell className="h-4 w-4 mr-2" />
                  Contact Support
                </SidebarMenuButton>
              </SidebarMenuItem>
              {userPermissions?.canManageBilling && (
                <SidebarMenuItem>
                  <SidebarMenuButton 
                    onClick={onCancelSubscription}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
                  >
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Cancel Subscription
                  </SidebarMenuButton>
                </SidebarMenuItem>
              )}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
