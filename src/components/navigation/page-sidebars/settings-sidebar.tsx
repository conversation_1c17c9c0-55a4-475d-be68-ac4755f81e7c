'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  Settings,
  User,
  Shield,
  CreditCard,
  Bell,
  Palette,
  Globe,
  Key,
  Database,
  Zap,
  Users,
  Building,
  HelpCircle,
  FileText,
  Download,
  Trash2,
  ChevronRight,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

import { PageSidebarProps } from '@/types/navigation';
import { cn } from '@/lib/utils';

// Settings navigation structure
const settingsNavigation = [
  {
    id: 'profile',
    title: 'Profile',
    href: '/dashboard/settings/profile',
    icon: User,
    description: 'Personal information and preferences',
  },
  {
    id: 'security',
    title: 'Security',
    href: '/dashboard/settings/security',
    icon: Shield,
    description: 'Password, 2FA, and security settings',
    badge: { text: 'Important', variant: 'destructive' as const },
  },
  {
    id: 'billing',
    title: 'Billing',
    href: '/dashboard/settings/billing',
    icon: CreditCard,
    description: 'Subscription and payment methods',
  },
  {
    id: 'notifications',
    title: 'Notifications',
    href: '/dashboard/settings/notifications',
    icon: Bell,
    description: 'Email and push notification preferences',
  },
  {
    id: 'appearance',
    title: 'Appearance',
    href: '/dashboard/settings/appearance',
    icon: Palette,
    description: 'Theme and display preferences',
  },
  {
    id: 'language',
    title: 'Language & Region',
    href: '/dashboard/settings/language',
    icon: Globe,
    description: 'Language, timezone, and regional settings',
  },
];

const advancedSettings = [
  {
    id: 'api-keys',
    title: 'API Keys',
    href: '/dashboard/settings/api-keys',
    icon: Key,
    description: 'Manage API keys and tokens',
    requiredRole: 'admin',
  },
  {
    id: 'integrations',
    title: 'Integrations',
    href: '/dashboard/settings/integrations',
    icon: Zap,
    description: 'Third-party service integrations',
  },
  {
    id: 'data',
    title: 'Data Management',
    href: '/dashboard/settings/data',
    icon: Database,
    description: 'Export, import, and backup data',
  },
  {
    id: 'team',
    title: 'Team Management',
    href: '/dashboard/settings/team',
    icon: Users,
    description: 'Manage team members and permissions',
    requiredRole: 'admin',
  },
  {
    id: 'organization',
    title: 'Organization',
    href: '/dashboard/settings/organization',
    icon: Building,
    description: 'Organization settings and branding',
    requiredRole: 'admin',
  },
];

const supportActions = [
  {
    id: 'help',
    title: 'Help Center',
    href: '/help',
    icon: HelpCircle,
    external: true,
  },
  {
    id: 'documentation',
    title: 'Documentation',
    href: '/docs',
    icon: FileText,
    external: true,
  },
  {
    id: 'export-data',
    title: 'Export Data',
    action: 'export',
    icon: Download,
  },
  {
    id: 'delete-account',
    title: 'Delete Account',
    action: 'delete',
    icon: Trash2,
    variant: 'destructive' as const,
  },
];

interface SettingsSidebarProps extends Omit<PageSidebarProps, 'config'> {
  userRole?: string;
  onSettingsAction?: (action: string) => void;
}

export function SettingsSidebar({
  userRole = 'user',
  onSettingsAction,
  onQuickAction,
  className,
  ...props
}: SettingsSidebarProps) {
  const pathname = usePathname();

  const isActive = (href: string) => pathname === href;

  const canAccess = (requiredRole?: string) => {
    if (!requiredRole) return true;
    return userRole === 'admin' || userRole === requiredRole;
  };

  const handleAction = (action: string) => {
    onSettingsAction?.(action);
  };

  return (
    <Sidebar className={cn('w-80 border-l', className)} {...props}>
      <SidebarHeader className="p-4 border-b">
        <div className="flex items-center gap-2 mb-4">
          <Settings className="h-5 w-5" />
          <h2 className="text-lg font-semibold">Settings</h2>
        </div>
        
        <p className="text-sm text-muted-foreground">
          Manage your account settings and preferences
        </p>
      </SidebarHeader>

      <SidebarContent>
        {/* General Settings */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">General</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <SidebarMenu>
              {settingsNavigation.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild isActive={isActive(item.href)}>
                      <Link href={item.href} className="group">
                        <item.icon className="h-4 w-4" />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span>{item.title}</span>
                            {item.badge && (
                              <Badge variant={item.badge.variant} className="text-xs">
                                {item.badge.text}
                              </Badge>
                            )}
                          </div>
                          <div className="text-xs text-muted-foreground mt-0.5">
                            {item.description}
                          </div>
                        </div>
                        <ChevronRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </motion.div>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="my-4" />

        {/* Advanced Settings */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Advanced</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <SidebarMenu>
              {advancedSettings
                .filter(item => canAccess(item.requiredRole))
                .map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: (settingsNavigation.length + index) * 0.05 }}
                  >
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild isActive={isActive(item.href)}>
                        <Link href={item.href} className="group">
                          <item.icon className="h-4 w-4" />
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <span>{item.title}</span>
                              {item.requiredRole === 'admin' && (
                                <Badge variant="outline" className="text-xs">
                                  Admin
                                </Badge>
                              )}
                            </div>
                            <div className="text-xs text-muted-foreground mt-0.5">
                              {item.description}
                            </div>
                          </div>
                          <ChevronRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </motion.div>
                ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="my-4" />

        {/* Support & Actions */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Support & Actions</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <SidebarMenu>
              {supportActions.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: (settingsNavigation.length + advancedSettings.length + index) * 0.05 }}
                >
                  <SidebarMenuItem>
                    {item.href ? (
                      <SidebarMenuButton asChild>
                        <Link 
                          href={item.href}
                          target={item.external ? '_blank' : undefined}
                          className="group"
                        >
                          <item.icon className="h-4 w-4" />
                          <span>{item.title}</span>
                          <ChevronRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                        </Link>
                      </SidebarMenuButton>
                    ) : (
                      <SidebarMenuButton
                        onClick={() => item.action && handleAction(item.action)}
                        className={cn(
                          'group',
                          item.variant === 'destructive' && 'text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950'
                        )}
                      >
                        <item.icon className="h-4 w-4" />
                        <span>{item.title}</span>
                        <ChevronRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                      </SidebarMenuButton>
                    )}
                  </SidebarMenuItem>
                </motion.div>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="my-4" />

        {/* Quick Actions */}
        <SidebarGroup>
          <SidebarGroupContent className="px-2">
            <div className="space-y-2">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => handleAction('backup')}
              >
                <Download className="h-4 w-4 mr-2" />
                Backup Settings
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => handleAction('reset')}
              >
                <Settings className="h-4 w-4 mr-2" />
                Reset to Defaults
              </Button>
            </div>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
