'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  LayoutDashboard,
  Activity,
  TrendingUp,
  TrendingDown,
  Minus,
  RefreshCw,
  Plus,
  Settings,
  Monitor,
  HardDrive,
  Cloud,
  Users,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  BarChart3,
  Eye,
  ExternalLink,
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

import { 
  DashboardSidebarProps, 
  StatItem, 
  ActivityItem, 
  SystemStatus,
  QuickAction 
} from '@/types/navigation';
import { cn } from '@/lib/utils';

interface DashboardOverviewSidebarProps extends DashboardSidebarProps {
  onCreateWorkspace?: () => void;
  onViewMonitoring?: () => void;
  onManageContainers?: () => void;
  onManageImages?: () => void;
}

export function DashboardOverviewSidebar({
  stats = [],
  activities = [],
  systemStatus = [],
  onRefreshStats,
  onViewActivity,
  onCreateWorkspace,
  onViewMonitoring,
  onManageContainers,
  onManageImages,
  onQuickAction,
  className,
  loading = false,
  ...props
}: DashboardOverviewSidebarProps) {
  const [refreshing, setRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Mock data if not provided
  const defaultStats: StatItem[] = [
    {
      id: 'workspaces',
      label: 'Active Workspaces',
      value: 12,
      change: { value: 8.2, type: 'increase', period: 'vs last week' },
      icon: Monitor,
      color: 'primary',
    },
    {
      id: 'containers',
      label: 'Running Containers',
      value: 24,
      change: { value: 3.1, type: 'increase', period: 'vs yesterday' },
      icon: HardDrive,
      color: 'success',
    },
    {
      id: 'images',
      label: 'Docker Images',
      value: 156,
      change: { value: 2.4, type: 'decrease', period: 'vs last month' },
      icon: Cloud,
      color: 'default',
    },
    {
      id: 'users',
      label: 'Active Users',
      value: 8,
      change: { value: 12.5, type: 'increase', period: 'vs last week' },
      icon: Users,
      color: 'primary',
    },
  ];

  const defaultActivities: ActivityItem[] = [
    {
      id: '1',
      type: 'success',
      title: 'Workspace Created',
      description: 'New Ubuntu workspace "dev-env-01" created successfully',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      icon: Plus,
      user: { name: 'John Doe' },
    },
    {
      id: '2',
      type: 'info',
      title: 'Container Started',
      description: 'Container "web-server" started on port 3000',
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      icon: Activity,
      user: { name: 'Jane Smith' },
    },
    {
      id: '3',
      type: 'warning',
      title: 'High Memory Usage',
      description: 'Workspace "data-analysis" using 85% memory',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      icon: AlertTriangle,
    },
    {
      id: '4',
      type: 'success',
      title: 'Image Built',
      description: 'Custom image "node-app:latest" built successfully',
      timestamp: new Date(Date.now() - 45 * 60 * 1000),
      icon: Cloud,
      user: { name: 'Mike Johnson' },
    },
  ];

  const defaultSystemStatus: SystemStatus[] = [
    {
      id: 'docker',
      name: 'Docker Engine',
      status: 'online',
      uptime: 99.9,
      lastCheck: new Date(),
      metrics: { cpu: 15, memory: 45, disk: 60 },
    },
    {
      id: 'guacamole',
      name: 'Guacamole Server',
      status: 'online',
      uptime: 98.5,
      lastCheck: new Date(),
      metrics: { cpu: 8, memory: 25, network: 30 },
    },
    {
      id: 'database',
      name: 'Database',
      status: 'online',
      uptime: 99.8,
      lastCheck: new Date(),
      metrics: { cpu: 12, memory: 35, disk: 40 },
    },
  ];

  const displayStats = stats.length > 0 ? stats : defaultStats;
  const displayActivities = activities.length > 0 ? activities : defaultActivities;
  const displaySystemStatus = systemStatus.length > 0 ? systemStatus : defaultSystemStatus;

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await onRefreshStats?.();
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Failed to refresh stats:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const getChangeIcon = (type: 'increase' | 'decrease' | 'neutral') => {
    switch (type) {
      case 'increase':
        return TrendingUp;
      case 'decrease':
        return TrendingDown;
      default:
        return Minus;
    }
  };

  const getChangeColor = (type: 'increase' | 'decrease' | 'neutral') => {
    switch (type) {
      case 'increase':
        return 'text-green-600 dark:text-green-400';
      case 'decrease':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return CheckCircle;
      case 'offline':
        return AlertTriangle;
      case 'degraded':
        return AlertTriangle;
      case 'maintenance':
        return Clock;
      default:
        return AlertTriangle;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'text-green-600 dark:text-green-400';
      case 'offline':
        return 'text-red-600 dark:text-red-400';
      case 'degraded':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'maintenance':
        return 'text-blue-600 dark:text-blue-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <Sidebar className={cn('w-80 border-l', className)} {...props}>
      <SidebarHeader className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <LayoutDashboard className="h-5 w-5" />
            Dashboard
          </h2>
          <Button
            onClick={handleRefresh}
            size="sm"
            variant="outline"
            disabled={refreshing}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={cn('h-4 w-4', refreshing && 'animate-spin')} />
          </Button>
        </div>
        
        <div className="text-xs text-muted-foreground">
          Last updated: {formatTimestamp(lastRefresh)}
        </div>
      </SidebarHeader>

      <SidebarContent>
        {/* Quick Stats */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Quick Stats</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <div className="grid grid-cols-2 gap-2">
              {displayStats.map((stat, index) => {
                const Icon = stat.icon || BarChart3;
                const ChangeIcon = stat.change ? getChangeIcon(stat.change.type) : null;
                
                return (
                  <motion.div
                    key={stat.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                  >
                    <div className="flex items-center gap-2 mb-1">
                      <Icon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-xs font-medium text-muted-foreground">
                        {stat.label}
                      </span>
                    </div>
                    <div className="text-lg font-bold">{stat.value}</div>
                    {stat.change && ChangeIcon && (
                      <div className={cn('flex items-center gap-1 text-xs', getChangeColor(stat.change.type))}>
                        <ChangeIcon className="h-3 w-3" />
                        <span>{stat.change.value}%</span>
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Quick Actions */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Quick Actions</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={onCreateWorkspace}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Workspace
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={onViewMonitoring}>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Monitoring
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={onManageContainers}>
                  <HardDrive className="h-4 w-4 mr-2" />
                  Manage Containers
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={onManageImages}>
                  <Cloud className="h-4 w-4 mr-2" />
                  Manage Images
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* System Status */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">System Status</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <div className="space-y-2">
              {displaySystemStatus.map((system, index) => {
                const StatusIcon = getStatusIcon(system.status);
                
                return (
                  <motion.div
                    key={system.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-3 rounded-lg border bg-card"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <StatusIcon className={cn('h-4 w-4', getStatusColor(system.status))} />
                        <span className="text-sm font-medium">{system.name}</span>
                      </div>
                      <Badge variant={system.status === 'online' ? 'default' : 'destructive'}>
                        {system.status}
                      </Badge>
                    </div>
                    {system.metrics && (
                      <div className="space-y-1">
                        {system.metrics.cpu && (
                          <div className="flex items-center justify-between text-xs">
                            <span>CPU</span>
                            <span>{system.metrics.cpu}%</span>
                          </div>
                        )}
                        {system.metrics.memory && (
                          <div className="flex items-center justify-between text-xs">
                            <span>Memory</span>
                            <span>{system.metrics.memory}%</span>
                          </div>
                        )}
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Recent Activity */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4 flex items-center justify-between">
            Recent Activity
            <Button
              onClick={() => onQuickAction?.({ 
                id: 'view-all-activity', 
                type: 'button', 
                title: 'View All', 
                onClick: () => {} 
              })}
              size="sm"
              variant="ghost"
              className="h-6 px-2 text-xs"
            >
              <Eye className="h-3 w-3 mr-1" />
              View All
            </Button>
          </SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <div className="space-y-2 max-h-64 overflow-y-auto">
              <AnimatePresence>
                {displayActivities.slice(0, 5).map((activity, index) => {
                  const Icon = activity.icon || Activity;
                  
                  return (
                    <motion.div
                      key={activity.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ delay: index * 0.05 }}
                      className="p-2 rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                      onClick={() => onViewActivity?.(activity)}
                    >
                      <div className="flex items-start gap-2">
                        <Icon className={cn(
                          'h-4 w-4 mt-0.5 flex-shrink-0',
                          activity.type === 'success' && 'text-green-600',
                          activity.type === 'warning' && 'text-yellow-600',
                          activity.type === 'error' && 'text-red-600',
                          activity.type === 'info' && 'text-blue-600'
                        )} />
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium truncate">
                            {activity.title}
                          </div>
                          {activity.description && (
                            <div className="text-xs text-muted-foreground line-clamp-2">
                              {activity.description}
                            </div>
                          )}
                          <div className="text-xs text-muted-foreground mt-1">
                            {formatTimestamp(activity.timestamp)}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            </div>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
