/**
 * Page Sidebars Index
 * Exports all page-specific sidebar components for the Omnispace platform
 */

// Dashboard page sidebar
export { DashboardOverviewSidebar } from './dashboard-overview-sidebar';

// Images page sidebar
export { ImagesSidebar } from './images-sidebar';

// Enhanced containers sidebar (existing)
export { EnhancedContainersSidebar } from './enhanced-containers-sidebar';

// Settings page sidebars
export { ProfileSettingsSidebar } from './profile-settings-sidebar';
export { SecuritySettingsSidebar } from './security-settings-sidebar';
export { BillingSettingsSidebar } from './billing-settings-sidebar';
export { TeamManagementSidebar } from './team-management-sidebar';

// Re-export types for convenience
export type {
  DashboardSidebarProps,
  ImagesSidebarProps,
  SettingsSidebarProps,
  PageSidebarProps,
  QuickAction,
  NavigationItem,
  StatItem,
  ActivityItem,
  SystemStatus,
  FilterGroup,
  UserPermissions,
  UserRole,
  Permission,
} from '@/types/navigation';

// Sidebar mapping for easy integration
export const PAGE_SIDEBARS = {
  // Dashboard pages
  '/dashboard': 'DashboardOverviewSidebar',
  '/dashboard/overview': 'DashboardOverviewSidebar',
  
  // Infrastructure pages
  '/dashboard/images': 'ImagesSidebar',
  '/dashboard/containers': 'EnhancedContainersSidebar',
  
  // Settings pages
  '/dashboard/settings/profile': 'ProfileSettingsSidebar',
  '/dashboard/settings/security': 'SecuritySettingsSidebar',
  '/dashboard/settings/billing': 'BillingSettingsSidebar',
  '/dashboard/settings/team': 'TeamManagementSidebar',
} as const;

// Sidebar component mapping for dynamic imports
export const SIDEBAR_COMPONENTS = {
  DashboardOverviewSidebar: () => import('./dashboard-overview-sidebar').then(m => m.DashboardOverviewSidebar),
  ImagesSidebar: () => import('./images-sidebar').then(m => m.ImagesSidebar),
  EnhancedContainersSidebar: () => import('./enhanced-containers-sidebar').then(m => m.EnhancedContainersSidebar),
  ProfileSettingsSidebar: () => import('./profile-settings-sidebar').then(m => m.ProfileSettingsSidebar),
  SecuritySettingsSidebar: () => import('./security-settings-sidebar').then(m => m.SecuritySettingsSidebar),
  BillingSettingsSidebar: () => import('./billing-settings-sidebar').then(m => m.BillingSettingsSidebar),
  TeamManagementSidebar: () => import('./team-management-sidebar').then(m => m.TeamManagementSidebar),
} as const;

// Helper function to get sidebar component name for a given path
export function getSidebarForPath(pathname: string): string | null {
  // Direct path match
  if (pathname in PAGE_SIDEBARS) {
    return PAGE_SIDEBARS[pathname as keyof typeof PAGE_SIDEBARS];
  }
  
  // Pattern matching for dynamic routes
  const patterns = [
    { pattern: /^\/dashboard\/settings\/profile/, sidebar: 'ProfileSettingsSidebar' },
    { pattern: /^\/dashboard\/settings\/security/, sidebar: 'SecuritySettingsSidebar' },
    { pattern: /^\/dashboard\/settings\/billing/, sidebar: 'BillingSettingsSidebar' },
    { pattern: /^\/dashboard\/settings\/team/, sidebar: 'TeamManagementSidebar' },
    { pattern: /^\/dashboard\/images/, sidebar: 'ImagesSidebar' },
    { pattern: /^\/dashboard\/containers/, sidebar: 'EnhancedContainersSidebar' },
    { pattern: /^\/dashboard\/?$/, sidebar: 'DashboardOverviewSidebar' },
  ];
  
  for (const { pattern, sidebar } of patterns) {
    if (pattern.test(pathname)) {
      return sidebar;
    }
  }
  
  return null;
}

// Helper function to check if a path has a sidebar
export function hasSidebar(pathname: string): boolean {
  return getSidebarForPath(pathname) !== null;
}

// Default sidebar configurations
export const DEFAULT_SIDEBAR_CONFIGS = {
  dashboard: {
    title: 'Dashboard Overview',
    description: 'Quick stats, activities, and system status',
    width: 320,
    collapsible: true,
    position: 'right' as const,
  },
  images: {
    title: 'Images Management',
    description: 'Filter, manage, and monitor Docker images',
    width: 320,
    collapsible: true,
    position: 'right' as const,
  },
  containers: {
    title: 'Container Management',
    description: 'Monitor and manage Docker containers',
    width: 320,
    collapsible: true,
    position: 'right' as const,
  },
  profile: {
    title: 'Profile Settings',
    description: 'Manage your profile and preferences',
    width: 320,
    collapsible: true,
    position: 'right' as const,
  },
  security: {
    title: 'Security Settings',
    description: 'Authentication and security controls',
    width: 320,
    collapsible: true,
    position: 'right' as const,
  },
  billing: {
    title: 'Billing & Usage',
    description: 'Subscription, usage, and payment management',
    width: 320,
    collapsible: true,
    position: 'right' as const,
  },
  team: {
    title: 'Team Management',
    description: 'Manage team members and permissions',
    width: 320,
    collapsible: true,
    position: 'right' as const,
  },
} as const;

// Sidebar permissions mapping
export const SIDEBAR_PERMISSIONS = {
  DashboardOverviewSidebar: ['read'],
  ImagesSidebar: ['read'],
  EnhancedContainersSidebar: ['read'],
  ProfileSettingsSidebar: ['read'],
  SecuritySettingsSidebar: ['read'],
  BillingSettingsSidebar: ['read', 'canViewBilling'],
  TeamManagementSidebar: ['read', 'canManageUsers'],
} as const;

// Helper function to check if user can access a sidebar
export function canAccessSidebar(
  sidebarName: string,
  userPermissions: { permissions: string[]; canViewBilling?: boolean; canManageUsers?: boolean }
): boolean {
  const requiredPermissions = SIDEBAR_PERMISSIONS[sidebarName as keyof typeof SIDEBAR_PERMISSIONS];
  
  if (!requiredPermissions) {
    return true; // Default to accessible if no specific permissions defined
  }
  
  return requiredPermissions.every(permission => {
    if (permission === 'canViewBilling') {
      return userPermissions.canViewBilling === true;
    }
    if (permission === 'canManageUsers') {
      return userPermissions.canManageUsers === true;
    }
    return userPermissions.permissions.includes(permission);
  });
}

// Export sidebar metadata for documentation and tooling
export const SIDEBAR_METADATA = {
  DashboardOverviewSidebar: {
    name: 'Dashboard Overview Sidebar',
    description: 'Displays quick stats, recent activities, system status, and quick actions for the dashboard overview page',
    features: ['Quick Stats', 'Recent Activity', 'System Status', 'Quick Actions'],
    permissions: ['read'],
  },
  ImagesSidebar: {
    name: 'Images Management Sidebar',
    description: 'Provides filtering, quick actions, registry information, and storage metrics for Docker images',
    features: ['Image Filtering', 'Registry Management', 'Storage Metrics', 'Quick Actions'],
    permissions: ['read'],
  },
  EnhancedContainersSidebar: {
    name: 'Enhanced Containers Sidebar',
    description: 'Advanced container management with local and remote container support',
    features: ['Local/Remote Containers', 'Container Actions', 'Status Monitoring', 'SSH Connections'],
    permissions: ['read'],
  },
  ProfileSettingsSidebar: {
    name: 'Profile Settings Sidebar',
    description: 'User profile management, preferences, and account settings',
    features: ['Profile Editing', 'Preferences', 'Notifications', 'Account Actions'],
    permissions: ['read'],
  },
  SecuritySettingsSidebar: {
    name: 'Security Settings Sidebar',
    description: 'Authentication options, session management, and security controls',
    features: ['2FA Management', 'Session Control', 'IP Whitelist', 'Security Score'],
    permissions: ['read'],
  },
  BillingSettingsSidebar: {
    name: 'Billing Settings Sidebar',
    description: 'Subscription information, usage metrics, and payment management',
    features: ['Usage Metrics', 'Payment Methods', 'Invoices', 'Plan Features'],
    permissions: ['read', 'canViewBilling'],
  },
  TeamManagementSidebar: {
    name: 'Team Management Sidebar',
    description: 'Team member management, role assignment, and team actions',
    features: ['Member Management', 'Role Assignment', 'Invitations', 'Team Stats'],
    permissions: ['read', 'canManageUsers'],
  },
} as const;
