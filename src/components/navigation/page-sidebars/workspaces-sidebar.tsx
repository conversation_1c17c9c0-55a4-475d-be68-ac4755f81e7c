'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Monitor,
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Clock,
  Star,
  Settings,
  Play,
  Square,
  MoreVertical,
  FolderOpen,
  Code,
  Terminal,
  Globe,
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

import { PageSidebarProps } from '@/types/navigation';
import { cn } from '@/lib/utils';

// Mock workspace data
const mockWorkspaces = [
  {
    id: 'ws-1',
    name: 'React Development',
    status: 'running',
    type: 'react',
    lastAccessed: '2 mins ago',
    resources: { cpu: 2, memory: 4096 },
  },
  {
    id: 'ws-2',
    name: 'Python ML Project',
    status: 'stopped',
    type: 'python',
    lastAccessed: '1 hour ago',
    resources: { cpu: 4, memory: 8192 },
  },
  {
    id: 'ws-3',
    name: 'Node.js API',
    status: 'starting',
    type: 'nodejs',
    lastAccessed: '30 mins ago',
    resources: { cpu: 1, memory: 2048 },
  },
];

interface WorkspacesSidebarProps extends Omit<PageSidebarProps, 'config'> {
  viewMode?: 'grid' | 'list';
  onViewModeChange?: (mode: 'grid' | 'list') => void;
  onCreateWorkspace?: () => void;
}

export function WorkspacesSidebar({
  viewMode = 'grid',
  onViewModeChange,
  onCreateWorkspace,
  onQuickAction,
  onFilterChange,
  className,
  ...props
}: WorkspacesSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  const filteredWorkspaces = mockWorkspaces.filter(workspace => {
    const matchesSearch = workspace.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || workspace.status === statusFilter;
    const matchesType = typeFilter === 'all' || workspace.type === typeFilter;
    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'stopped': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'starting': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'react': return Code;
      case 'python': return Terminal;
      case 'nodejs': return Globe;
      default: return FolderOpen;
    }
  };

  return (
    <Sidebar className={cn('w-80 border-l', className)} {...props}>
      <SidebarHeader className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Workspaces</h2>
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="icon"
              onClick={() => onViewModeChange?.('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="icon"
              onClick={() => onViewModeChange?.('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search workspaces..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Filters */}
        <div className="flex gap-2 mb-4">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="running">Running</SelectItem>
              <SelectItem value="stopped">Stopped</SelectItem>
              <SelectItem value="starting">Starting</SelectItem>
            </SelectContent>
          </Select>

          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="react">React</SelectItem>
              <SelectItem value="python">Python</SelectItem>
              <SelectItem value="nodejs">Node.js</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Quick Actions */}
        <Button 
          onClick={onCreateWorkspace}
          className="w-full"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Workspace
        </Button>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">
            Active Workspaces ({filteredWorkspaces.length})
          </SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <AnimatePresence mode="popLayout">
              {filteredWorkspaces.map((workspace, index) => {
                const TypeIcon = getTypeIcon(workspace.type);
                
                return (
                  <motion.div
                    key={workspace.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.05 }}
                    className="mb-2"
                  >
                    <SidebarMenuItem>
                      <div className="group relative p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <TypeIcon className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium text-sm">{workspace.name}</span>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <MoreVertical className="h-3 w-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <Play className="h-4 w-4 mr-2" />
                                Start
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Square className="h-4 w-4 mr-2" />
                                Stop
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Settings className="h-4 w-4 mr-2" />
                                Settings
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        <div className="flex items-center gap-2 mb-2">
                          <Badge className={cn('text-xs', getStatusColor(workspace.status))}>
                            {workspace.status}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {workspace.resources.cpu} CPU, {workspace.resources.memory}MB
                          </span>
                        </div>

                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {workspace.lastAccessed}
                          </span>
                          <Button variant="ghost" size="sm" className="h-6 px-2">
                            Open
                          </Button>
                        </div>
                      </div>
                    </SidebarMenuItem>
                  </motion.div>
                );
              })}
            </AnimatePresence>

            {filteredWorkspaces.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Monitor className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No workspaces found</p>
                {searchQuery && (
                  <p className="text-xs mt-1">Try adjusting your search or filters</p>
                )}
              </div>
            )}
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="my-4" />

        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Recent Templates</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <SidebarMenu>
              {['React + TypeScript', 'Python ML', 'Node.js Express', 'Vue.js'].map((template) => (
                <SidebarMenuItem key={template}>
                  <SidebarMenuButton className="justify-start">
                    <Star className="h-4 w-4 mr-2" />
                    {template}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
