'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Cloud,
  Download,
  Upload,
  Search,
  Filter,
  Plus,
  Trash2,
  RefreshCw,
  HardDrive,
  Tag,
  Calendar,
  FileText,
  ExternalLink,
  Database,
  Layers,
  Package,
  Globe,
  Lock,
  Unlock,
  Star,
  Clock,
  AlertTriangle,
  CheckCircle,
  Info,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

import { ImagesSidebarProps, FilterGroup, StatItem } from '@/types/navigation';
import { cn } from '@/lib/utils';

interface ImageInfo {
  id: string;
  name: string;
  tag: string;
  size: number;
  created: Date;
  registry?: string;
  isOfficial?: boolean;
  isPrivate?: boolean;
  layers: number;
  pulls?: number;
  stars?: number;
  description?: string;
  status: 'available' | 'pulling' | 'building' | 'error';
}

interface RegistryInfo {
  id: string;
  name: string;
  url: string;
  type: 'docker-hub' | 'private' | 'aws-ecr' | 'gcr' | 'azure-acr';
  status: 'connected' | 'disconnected' | 'error';
  imageCount: number;
  lastSync?: Date;
}

interface ImagesSidebarExtendedProps extends ImagesSidebarProps {
  onFilterChange?: (filters: Record<string, any>) => void;
  onSearchChange?: (query: string) => void;
  onImageAction?: (imageId: string, action: string) => void;
  onRegistryAction?: (registryId: string, action: string) => void;
}

export function ImagesSidebar({
  onPullImage,
  onBuildImage,
  onManageRegistry,
  onViewStorage,
  onFilterChange,
  onSearchChange,
  onImageAction,
  onRegistryAction,
  onQuickAction,
  className,
  loading = false,
  ...props
}: ImagesSidebarExtendedProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState<Record<string, any>>({
    type: 'all',
    registry: 'all',
    status: 'all',
    size: 'all',
  });
  const [activeTab, setActiveTab] = useState<'filters' | 'registries' | 'storage'>('filters');

  // Mock data
  const storageStats: StatItem[] = [
    {
      id: 'total-size',
      label: 'Total Size',
      value: '12.4 GB',
      icon: HardDrive,
      color: 'primary',
    },
    {
      id: 'image-count',
      label: 'Total Images',
      value: 156,
      change: { value: 8, type: 'increase', period: 'this week' },
      icon: Package,
      color: 'success',
    },
    {
      id: 'layers',
      label: 'Unique Layers',
      value: 1247,
      icon: Layers,
      color: 'default',
    },
    {
      id: 'registries',
      label: 'Registries',
      value: 4,
      icon: Globe,
      color: 'primary',
    },
  ];

  const registries: RegistryInfo[] = [
    {
      id: 'docker-hub',
      name: 'Docker Hub',
      url: 'https://hub.docker.com',
      type: 'docker-hub',
      status: 'connected',
      imageCount: 89,
      lastSync: new Date(Date.now() - 30 * 60 * 1000),
    },
    {
      id: 'private-registry',
      name: 'Private Registry',
      url: 'registry.company.com',
      type: 'private',
      status: 'connected',
      imageCount: 45,
      lastSync: new Date(Date.now() - 15 * 60 * 1000),
    },
    {
      id: 'aws-ecr',
      name: 'AWS ECR',
      url: 'amazonaws.com/ecr',
      type: 'aws-ecr',
      status: 'disconnected',
      imageCount: 22,
      lastSync: new Date(Date.now() - 2 * 60 * 60 * 1000),
    },
  ];

  const filterGroups: FilterGroup[] = [
    {
      id: 'type',
      label: 'Image Type',
      type: 'select',
      options: [
        { id: 'all', label: 'All Types', value: 'all' },
        { id: 'official', label: 'Official', value: 'official', icon: CheckCircle },
        { id: 'private', label: 'Private', value: 'private', icon: Lock },
        { id: 'public', label: 'Public', value: 'public', icon: Unlock },
      ],
    },
    {
      id: 'registry',
      label: 'Registry',
      type: 'select',
      options: [
        { id: 'all', label: 'All Registries', value: 'all' },
        { id: 'docker-hub', label: 'Docker Hub', value: 'docker-hub' },
        { id: 'private', label: 'Private', value: 'private' },
        { id: 'aws-ecr', label: 'AWS ECR', value: 'aws-ecr' },
      ],
    },
    {
      id: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { id: 'all', label: 'All Status', value: 'all' },
        { id: 'available', label: 'Available', value: 'available', icon: CheckCircle },
        { id: 'pulling', label: 'Pulling', value: 'pulling', icon: Download },
        { id: 'building', label: 'Building', value: 'building', icon: Package },
        { id: 'error', label: 'Error', value: 'error', icon: AlertTriangle },
      ],
    },
    {
      id: 'size',
      label: 'Size Range',
      type: 'select',
      options: [
        { id: 'all', label: 'All Sizes', value: 'all' },
        { id: 'small', label: '< 100 MB', value: 'small' },
        { id: 'medium', label: '100 MB - 1 GB', value: 'medium' },
        { id: 'large', label: '> 1 GB', value: 'large' },
      ],
    },
  ];

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    onSearchChange?.(value);
  };

  const handleFilterChange = (filterId: string, value: string) => {
    const newFilters = { ...selectedFilters, [filterId]: value };
    setSelectedFilters(newFilters);
    onFilterChange?.(newFilters);
  };

  const getRegistryIcon = (type: string) => {
    switch (type) {
      case 'docker-hub':
        return Cloud;
      case 'private':
        return Lock;
      case 'aws-ecr':
        return Database;
      default:
        return Globe;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return CheckCircle;
      case 'disconnected':
        return AlertTriangle;
      case 'error':
        return AlertTriangle;
      default:
        return Info;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'text-green-600 dark:text-green-400';
      case 'disconnected':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'error':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const formatSize = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    return `${hours}h ago`;
  };

  return (
    <Sidebar className={cn('w-80 border-l', className)} {...props}>
      <SidebarHeader className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Cloud className="h-5 w-5" />
            Images
          </h2>
          <Button onClick={onBuildImage} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Build
          </Button>
        </div>

        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search images..."
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="filters" className="text-xs">
              <Filter className="h-3 w-3 mr-1" />
              Filters
            </TabsTrigger>
            <TabsTrigger value="registries" className="text-xs">
              <Globe className="h-3 w-3 mr-1" />
              Registries
            </TabsTrigger>
            <TabsTrigger value="storage" className="text-xs">
              <HardDrive className="h-3 w-3 mr-1" />
              Storage
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </SidebarHeader>

      <SidebarContent>
        <Tabs value={activeTab} className="w-full">
          {/* Filters Tab */}
          <TabsContent value="filters" className="mt-0">
            <SidebarGroup>
              <SidebarGroupLabel className="px-4">Filter Images</SidebarGroupLabel>
              <SidebarGroupContent className="px-4 space-y-4">
                {filterGroups.map((group) => (
                  <div key={group.id}>
                    <label className="text-sm font-medium mb-2 block">
                      {group.label}
                    </label>
                    <Select
                      value={selectedFilters[group.id]}
                      onValueChange={(value) => handleFilterChange(group.id, value)}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {group.options.map((option) => {
                          const Icon = option.icon;
                          return (
                            <SelectItem key={option.id} value={option.value}>
                              <div className="flex items-center gap-2">
                                {Icon && <Icon className="h-4 w-4" />}
                                {option.label}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>
                ))}
              </SidebarGroupContent>
            </SidebarGroup>

            <Separator className="mx-4" />

            {/* Quick Actions */}
            <SidebarGroup>
              <SidebarGroupLabel className="px-4">Quick Actions</SidebarGroupLabel>
              <SidebarGroupContent className="px-2">
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton onClick={() => onPullImage?.('')}>
                      <Download className="h-4 w-4 mr-2" />
                      Pull Image
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  <SidebarMenuItem>
                    <SidebarMenuButton onClick={onBuildImage}>
                      <Package className="h-4 w-4 mr-2" />
                      Build Image
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  <SidebarMenuItem>
                    <SidebarMenuButton onClick={onManageRegistry}>
                      <Globe className="h-4 w-4 mr-2" />
                      Manage Registries
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  <SidebarMenuItem>
                    <SidebarMenuButton onClick={onViewStorage}>
                      <HardDrive className="h-4 w-4 mr-2" />
                      Storage Usage
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </TabsContent>

          {/* Registries Tab */}
          <TabsContent value="registries" className="mt-0">
            <SidebarGroup>
              <SidebarGroupLabel className="px-4">Connected Registries</SidebarGroupLabel>
              <SidebarGroupContent className="px-2">
                <div className="space-y-2">
                  {registries.map((registry, index) => {
                    const RegistryIcon = getRegistryIcon(registry.type);
                    const StatusIcon = getStatusIcon(registry.status);
                    
                    return (
                      <motion.div
                        key={registry.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <RegistryIcon className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <div className="font-medium text-sm">{registry.name}</div>
                              <div className="text-xs text-muted-foreground">{registry.url}</div>
                            </div>
                          </div>
                          <StatusIcon className={cn('h-4 w-4', getStatusColor(registry.status))} />
                        </div>
                        
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>{registry.imageCount} images</span>
                          {registry.lastSync && (
                            <span>Synced {formatTimestamp(registry.lastSync)}</span>
                          )}
                        </div>
                        
                        <div className="flex gap-1 mt-2">
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-6 px-2 text-xs flex-1"
                            onClick={() => onRegistryAction?.(registry.id, 'sync')}
                          >
                            <RefreshCw className="h-3 w-3 mr-1" />
                            Sync
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-6 px-2 text-xs flex-1"
                            onClick={() => onRegistryAction?.(registry.id, 'configure')}
                          >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            Configure
                          </Button>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </SidebarGroupContent>
            </SidebarGroup>
          </TabsContent>

          {/* Storage Tab */}
          <TabsContent value="storage" className="mt-0">
            <SidebarGroup>
              <SidebarGroupLabel className="px-4">Storage Statistics</SidebarGroupLabel>
              <SidebarGroupContent className="px-2">
                <div className="grid grid-cols-2 gap-2 mb-4">
                  {storageStats.map((stat, index) => {
                    const Icon = stat.icon || HardDrive;
                    
                    return (
                      <motion.div
                        key={stat.id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                        className="p-3 rounded-lg border bg-card"
                      >
                        <div className="flex items-center gap-2 mb-1">
                          <Icon className="h-4 w-4 text-muted-foreground" />
                          <span className="text-xs font-medium text-muted-foreground">
                            {stat.label}
                          </span>
                        </div>
                        <div className="text-lg font-bold">{stat.value}</div>
                        {stat.change && (
                          <div className="text-xs text-green-600 dark:text-green-400">
                            +{stat.change.value} {stat.change.period}
                          </div>
                        )}
                      </motion.div>
                    );
                  })}
                </div>

                {/* Storage Usage Breakdown */}
                <div className="space-y-3">
                  <div className="text-sm font-medium">Storage Breakdown</div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-xs">
                      <span>Base Images</span>
                      <span>8.2 GB (66%)</span>
                    </div>
                    <Progress value={66} className="h-2" />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-xs">
                      <span>Custom Images</span>
                      <span>3.1 GB (25%)</span>
                    </div>
                    <Progress value={25} className="h-2" />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-xs">
                      <span>Cached Layers</span>
                      <span>1.1 GB (9%)</span>
                    </div>
                    <Progress value={9} className="h-2" />
                  </div>
                </div>

                <Button
                  onClick={onViewStorage}
                  variant="outline"
                  className="w-full mt-4"
                  size="sm"
                >
                  <HardDrive className="h-4 w-4 mr-2" />
                  View Detailed Storage
                </Button>
              </SidebarGroupContent>
            </SidebarGroup>
          </TabsContent>
        </Tabs>
      </SidebarContent>
    </Sidebar>
  );
}
