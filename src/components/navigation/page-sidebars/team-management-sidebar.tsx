'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  UserPlus,
  UserMinus,
  Crown,
  Shield,
  Edit3,
  Eye,
  Settings,
  Mail,
  Phone,
  Calendar,
  Clock,
  Activity,
  Search,
  Filter,
  MoreVertical,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  Send,
  Copy,
  ExternalLink,
  Download,
  Upload,
  RefreshCw,
  Plus,
  Trash2,
  Ban,
  Unlock,
  Key,
  Globe,
  MapPin,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

import { SettingsSidebarProps, UserRole, UserPermissions } from '@/types/navigation';
import { cn } from '@/lib/utils';

interface TeamMember {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: UserRole;
  status: 'active' | 'invited' | 'suspended' | 'inactive';
  joinDate: Date;
  lastActive: Date;
  permissions: string[];
  department?: string;
  location?: string;
  workspaceAccess: string[];
  isOnline: boolean;
}

interface TeamInvitation {
  id: string;
  email: string;
  role: UserRole;
  invitedBy: string;
  invitedAt: Date;
  expiresAt: Date;
  status: 'pending' | 'expired' | 'accepted' | 'declined';
}

interface TeamStats {
  totalMembers: number;
  activeMembers: number;
  pendingInvitations: number;
  onlineMembers: number;
  roleDistribution: Record<UserRole, number>;
}

interface TeamManagementSidebarProps extends SettingsSidebarProps {
  teamMembers?: TeamMember[];
  pendingInvitations?: TeamInvitation[];
  teamStats?: TeamStats;
  onInviteMember?: (email: string, role: UserRole) => void;
  onRemoveMember?: (memberId: string) => void;
  onUpdateMemberRole?: (memberId: string, role: UserRole) => void;
  onSuspendMember?: (memberId: string) => void;
  onReactivateMember?: (memberId: string) => void;
  onResendInvitation?: (invitationId: string) => void;
  onCancelInvitation?: (invitationId: string) => void;
  onExportTeamData?: () => void;
  onBulkAction?: (action: string, memberIds: string[]) => void;
}

export function TeamManagementSidebar({
  teamMembers = [],
  pendingInvitations = [],
  teamStats,
  userPermissions,
  onInviteMember,
  onRemoveMember,
  onUpdateMemberRole,
  onSuspendMember,
  onReactivateMember,
  onResendInvitation,
  onCancelInvitation,
  onExportTeamData,
  onBulkAction,
  onQuickAction,
  className,
  loading = false,
  ...props
}: TeamManagementSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);

  // Mock data if not provided
  const defaultMembers: TeamMember[] = [
    {
      id: 'member-1',
      name: 'John Doe',
      email: '<EMAIL>',
      avatar: '/avatars/john-doe.jpg',
      role: 'owner',
      status: 'active',
      joinDate: new Date('2023-01-15'),
      lastActive: new Date(),
      permissions: ['read', 'write', 'admin'],
      department: 'Engineering',
      location: 'San Francisco, CA',
      workspaceAccess: ['workspace-1', 'workspace-2', 'workspace-3'],
      isOnline: true,
    },
    {
      id: 'member-2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      avatar: '/avatars/jane-smith.jpg',
      role: 'admin',
      status: 'active',
      joinDate: new Date('2023-02-20'),
      lastActive: new Date(Date.now() - 30 * 60 * 1000),
      permissions: ['read', 'write', 'admin'],
      department: 'Engineering',
      location: 'New York, NY',
      workspaceAccess: ['workspace-1', 'workspace-2'],
      isOnline: true,
    },
    {
      id: 'member-3',
      name: 'Mike Johnson',
      email: '<EMAIL>',
      role: 'editor',
      status: 'active',
      joinDate: new Date('2023-03-10'),
      lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000),
      permissions: ['read', 'write'],
      department: 'Design',
      location: 'Austin, TX',
      workspaceAccess: ['workspace-1'],
      isOnline: false,
    },
    {
      id: 'member-4',
      name: 'Sarah Wilson',
      email: '<EMAIL>',
      role: 'viewer',
      status: 'invited',
      joinDate: new Date('2024-01-05'),
      lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000),
      permissions: ['read'],
      department: 'Marketing',
      workspaceAccess: [],
      isOnline: false,
    },
  ];

  const defaultInvitations: TeamInvitation[] = [
    {
      id: 'inv-1',
      email: '<EMAIL>',
      role: 'editor',
      invitedBy: 'John Doe',
      invitedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      expiresAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
      status: 'pending',
    },
    {
      id: 'inv-2',
      email: '<EMAIL>',
      role: 'viewer',
      invitedBy: 'Jane Smith',
      invitedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      expiresAt: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000),
      status: 'pending',
    },
  ];

  const defaultStats: TeamStats = {
    totalMembers: 4,
    activeMembers: 3,
    pendingInvitations: 2,
    onlineMembers: 2,
    roleDistribution: {
      owner: 1,
      admin: 1,
      editor: 1,
      viewer: 1,
    },
  };

  const members = teamMembers.length > 0 ? teamMembers : defaultMembers;
  const invitations = pendingInvitations.length > 0 ? pendingInvitations : defaultInvitations;
  const stats = teamStats || defaultStats;

  // Filter members based on search and filters
  const filteredMembers = members.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesRole = roleFilter === 'all' || member.role === roleFilter;
    const matchesStatus = statusFilter === 'all' || member.status === statusFilter;
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'owner':
        return Crown;
      case 'admin':
        return Shield;
      case 'editor':
        return Edit3;
      case 'viewer':
        return Eye;
      default:
        return Users;
    }
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case 'owner':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'admin':
        return 'text-purple-600 dark:text-purple-400';
      case 'editor':
        return 'text-blue-600 dark:text-blue-400';
      case 'viewer':
        return 'text-green-600 dark:text-green-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return CheckCircle;
      case 'invited':
        return Clock;
      case 'suspended':
        return Ban;
      case 'inactive':
        return XCircle;
      default:
        return Info;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 dark:text-green-400';
      case 'invited':
        return 'text-blue-600 dark:text-blue-400';
      case 'suspended':
        return 'text-red-600 dark:text-red-400';
      case 'inactive':
        return 'text-gray-600 dark:text-gray-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const canManageMembers = userPermissions?.canManageUsers || userPermissions?.role === 'admin' || userPermissions?.role === 'owner';

  return (
    <Sidebar className={cn('w-80 border-l', className)} {...props}>
      <SidebarHeader className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Users className="h-5 w-5" />
            Team Management
          </h2>
          {canManageMembers && (
            <Button 
              onClick={() => onQuickAction?.({ 
                id: 'invite-member', 
                type: 'button', 
                title: 'Invite Member', 
                onClick: () => {} 
              })} 
              size="sm"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Invite
            </Button>
          )}
        </div>

        {/* Team Stats */}
        <div className="grid grid-cols-2 gap-2 mb-4">
          <div className="p-3 rounded-lg border bg-card">
            <div className="text-2xl font-bold">{stats.totalMembers}</div>
            <div className="text-xs text-muted-foreground">Total Members</div>
          </div>
          <div className="p-3 rounded-lg border bg-card">
            <div className="text-2xl font-bold text-green-600">{stats.onlineMembers}</div>
            <div className="text-xs text-muted-foreground">Online Now</div>
          </div>
        </div>

        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search members..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Filters */}
        <div className="grid grid-cols-2 gap-2">
          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger className="h-8">
              <SelectValue placeholder="Role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              <SelectItem value="owner">Owner</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="editor">Editor</SelectItem>
              <SelectItem value="viewer">Viewer</SelectItem>
            </SelectContent>
          </Select>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="h-8">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="invited">Invited</SelectItem>
              <SelectItem value="suspended">Suspended</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </SidebarHeader>

      <SidebarContent>
        {/* Team Members */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">
            Team Members ({filteredMembers.length})
          </SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <div className="space-y-2 max-h-96 overflow-y-auto">
              <AnimatePresence>
                {filteredMembers.map((member, index) => {
                  const RoleIcon = getRoleIcon(member.role);
                  const StatusIcon = getStatusIcon(member.status);
                  
                  return (
                    <motion.div
                      key={member.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ delay: index * 0.05 }}
                      className="p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                    >
                      <div className="flex items-start gap-3">
                        <div className="relative">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={member.avatar} alt={member.name} />
                            <AvatarFallback className="text-xs">
                              {member.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          {member.isOnline && (
                            <div className="absolute -bottom-0.5 -right-0.5 h-3 w-3 bg-green-500 border-2 border-background rounded-full" />
                          )}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-sm truncate">{member.name}</span>
                            <RoleIcon className={cn('h-3 w-3', getRoleColor(member.role))} />
                          </div>
                          <div className="text-xs text-muted-foreground truncate mb-1">
                            {member.email}
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {member.role}
                            </Badge>
                            <StatusIcon className={cn('h-3 w-3', getStatusColor(member.status))} />
                          </div>
                          {member.department && (
                            <div className="text-xs text-muted-foreground mt-1">
                              {member.department}
                            </div>
                          )}
                          <div className="text-xs text-muted-foreground">
                            Last active: {formatTimestamp(member.lastActive)}
                          </div>
                        </div>
                        
                        {canManageMembers && member.role !== 'owner' && (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                                <MoreVertical className="h-3 w-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => onUpdateMemberRole?.(member.id, 'admin')}>
                                <Shield className="h-4 w-4 mr-2" />
                                Change Role
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => onQuickAction?.({ 
                                id: 'edit-permissions', 
                                type: 'button', 
                                title: 'Edit Permissions', 
                                onClick: () => {} 
                              })}>
                                <Key className="h-4 w-4 mr-2" />
                                Edit Permissions
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {member.status === 'active' ? (
                                <DropdownMenuItem 
                                  onClick={() => onSuspendMember?.(member.id)}
                                  className="text-yellow-600"
                                >
                                  <Ban className="h-4 w-4 mr-2" />
                                  Suspend
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem 
                                  onClick={() => onReactivateMember?.(member.id)}
                                  className="text-green-600"
                                >
                                  <Unlock className="h-4 w-4 mr-2" />
                                  Reactivate
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem 
                                onClick={() => onRemoveMember?.(member.id)}
                                className="text-red-600"
                              >
                                <UserMinus className="h-4 w-4 mr-2" />
                                Remove
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}
                      </div>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        {invitations.length > 0 && (
          <>
            <Separator className="mx-4" />

            {/* Pending Invitations */}
            <SidebarGroup>
              <SidebarGroupLabel className="px-4">
                Pending Invitations ({invitations.length})
              </SidebarGroupLabel>
              <SidebarGroupContent className="px-2">
                <div className="space-y-2">
                  {invitations.map((invitation, index) => (
                    <motion.div
                      key={invitation.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="p-3 rounded-lg border bg-card"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <div className="font-medium text-sm">{invitation.email}</div>
                          <div className="text-xs text-muted-foreground">
                            Invited as {invitation.role}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            by {invitation.invitedBy}
                          </div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {invitation.status}
                        </Badge>
                      </div>
                      
                      {canManageMembers && (
                        <div className="flex gap-1">
                          <Button
                            onClick={() => onResendInvitation?.(invitation.id)}
                            size="sm"
                            variant="outline"
                            className="h-6 px-2 text-xs flex-1"
                          >
                            <Send className="h-3 w-3 mr-1" />
                            Resend
                          </Button>
                          <Button
                            onClick={() => onCancelInvitation?.(invitation.id)}
                            size="sm"
                            variant="outline"
                            className="h-6 px-2 text-xs"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              </SidebarGroupContent>
            </SidebarGroup>
          </>
        )}

        <Separator className="mx-4" />

        {/* Role Distribution */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Role Distribution</SidebarGroupLabel>
          <SidebarGroupContent className="px-4">
            <div className="space-y-2">
              {Object.entries(stats.roleDistribution).map(([role, count]) => {
                const RoleIcon = getRoleIcon(role as UserRole);
                return (
                  <div key={role} className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <RoleIcon className={cn('h-4 w-4', getRoleColor(role as UserRole))} />
                      <span className="capitalize">{role}</span>
                    </div>
                    <Badge variant="secondary">{count}</Badge>
                  </div>
                );
              })}
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Quick Actions */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Team Actions</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <SidebarMenu>
              {canManageMembers && (
                <>
                  <SidebarMenuItem>
                    <SidebarMenuButton onClick={() => onQuickAction?.({ 
                      id: 'invite-member', 
                      type: 'button', 
                      title: 'Invite Member', 
                      onClick: () => {} 
                    })}>
                      <UserPlus className="h-4 w-4 mr-2" />
                      Invite Member
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  <SidebarMenuItem>
                    <SidebarMenuButton onClick={() => onQuickAction?.({ 
                      id: 'bulk-invite', 
                      type: 'button', 
                      title: 'Bulk Invite', 
                      onClick: () => {} 
                    })}>
                      <Upload className="h-4 w-4 mr-2" />
                      Bulk Invite
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </>
              )}
              <SidebarMenuItem>
                <SidebarMenuButton onClick={onExportTeamData}>
                  <Download className="h-4 w-4 mr-2" />
                  Export Team Data
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={() => onQuickAction?.({ 
                  id: 'team-settings', 
                  type: 'button', 
                  title: 'Team Settings', 
                  onClick: () => {} 
                })}>
                  <Settings className="h-4 w-4 mr-2" />
                  Team Settings
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
