'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Edit3,
  Camera,
  Save,
  X,
  Check,
  Settings,
  Bell,
  Shield,
  Palette,
  Globe,
  Monitor,
  Moon,
  Sun,
  Smartphone,
  Key,
  Eye,
  EyeOff,
  AlertTriangle,
  CheckCircle,
  Clock,
  Activity,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

import { SettingsSidebarProps, UserPermissions } from '@/types/navigation';
import { cn } from '@/lib/utils';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  location?: string;
  bio?: string;
  avatar?: string;
  joinDate: Date;
  lastLogin: Date;
  isVerified: boolean;
  role: string;
  department?: string;
  timezone: string;
  language: string;
}

interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  notifications: {
    email: boolean;
    push: boolean;
    desktop: boolean;
    workspaceUpdates: boolean;
    systemAlerts: boolean;
    weeklyReports: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'team' | 'private';
    showOnlineStatus: boolean;
    allowDirectMessages: boolean;
  };
}

interface ProfileSettingsSidebarProps extends SettingsSidebarProps {
  userProfile?: UserProfile;
  userPreferences?: UserPreferences;
  onProfileUpdate?: (updates: Partial<UserProfile>) => void;
  onPreferencesUpdate?: (updates: Partial<UserPreferences>) => void;
  onAvatarUpload?: (file: File) => void;
  onPasswordChange?: () => void;
  onAccountDeactivate?: () => void;
}

export function ProfileSettingsSidebar({
  userProfile,
  userPreferences,
  userPermissions,
  onProfileUpdate,
  onPreferencesUpdate,
  onAvatarUpload,
  onPasswordChange,
  onAccountDeactivate,
  onQuickAction,
  className,
  loading = false,
  ...props
}: ProfileSettingsSidebarProps) {
  const [editingProfile, setEditingProfile] = useState(false);
  const [profileForm, setProfileForm] = useState<Partial<UserProfile>>({});
  const [showPassword, setShowPassword] = useState(false);

  // Mock data if not provided
  const defaultProfile: UserProfile = {
    id: 'user-1',
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    bio: 'Full-stack developer passionate about cloud technologies and containerization.',
    avatar: '/avatars/john-doe.jpg',
    joinDate: new Date('2023-01-15'),
    lastLogin: new Date(),
    isVerified: true,
    role: 'Senior Developer',
    department: 'Engineering',
    timezone: 'America/Los_Angeles',
    language: 'en',
  };

  const defaultPreferences: UserPreferences = {
    theme: 'system',
    language: 'en',
    timezone: 'America/Los_Angeles',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    notifications: {
      email: true,
      push: true,
      desktop: false,
      workspaceUpdates: true,
      systemAlerts: true,
      weeklyReports: false,
    },
    privacy: {
      profileVisibility: 'team',
      showOnlineStatus: true,
      allowDirectMessages: true,
    },
  };

  const defaultPermissions: UserPermissions = {
    role: 'editor',
    permissions: ['read', 'write'],
    canCreate: true,
    canEdit: true,
    canDelete: false,
    canManageUsers: false,
    canManageSettings: true,
    canViewBilling: false,
    canManageBilling: false,
  };

  const profile = userProfile || defaultProfile;
  const preferences = userPreferences || defaultPreferences;
  const permissions = userPermissions || defaultPermissions;

  const handleProfileEdit = () => {
    setEditingProfile(true);
    setProfileForm({
      name: profile.name,
      phone: profile.phone,
      location: profile.location,
      bio: profile.bio,
      department: profile.department,
    });
  };

  const handleProfileSave = () => {
    onProfileUpdate?.(profileForm);
    setEditingProfile(false);
    setProfileForm({});
  };

  const handleProfileCancel = () => {
    setEditingProfile(false);
    setProfileForm({});
  };

  const handlePreferenceChange = (key: string, value: any) => {
    const updates = { [key]: value };
    onPreferencesUpdate?.(updates);
  };

  const handleNotificationChange = (key: string, value: boolean) => {
    const updates = {
      notifications: {
        ...preferences.notifications,
        [key]: value,
      },
    };
    onPreferencesUpdate?.(updates);
  };

  const handlePrivacyChange = (key: string, value: any) => {
    const updates = {
      privacy: {
        ...preferences.privacy,
        [key]: value,
      },
    };
    onPreferencesUpdate?.(updates);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  const getThemeIcon = (theme: string) => {
    switch (theme) {
      case 'light':
        return Sun;
      case 'dark':
        return Moon;
      default:
        return Monitor;
    }
  };

  return (
    <Sidebar className={cn('w-80 border-l', className)} {...props}>
      <SidebarHeader className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <User className="h-5 w-5" />
            Profile Settings
          </h2>
          {!editingProfile && (
            <Button onClick={handleProfileEdit} size="sm" variant="outline">
              <Edit3 className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
        </div>

        {/* Profile Card */}
        <div className="p-4 rounded-lg border bg-card">
          <div className="flex items-start gap-3">
            <div className="relative">
              <Avatar className="h-12 w-12">
                <AvatarImage src={profile.avatar} alt={profile.name} />
                <AvatarFallback>
                  {profile.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <Button
                size="sm"
                variant="outline"
                className="absolute -bottom-1 -right-1 h-6 w-6 p-0 rounded-full"
                onClick={() => document.getElementById('avatar-upload')?.click()}
              >
                <Camera className="h-3 w-3" />
              </Button>
              <input
                id="avatar-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) onAvatarUpload?.(file);
                }}
              />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold truncate">{profile.name}</h3>
                {profile.isVerified && (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                )}
              </div>
              <p className="text-sm text-muted-foreground">{profile.email}</p>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="secondary" className="text-xs">
                  {profile.role}
                </Badge>
                {profile.department && (
                  <Badge variant="outline" className="text-xs">
                    {profile.department}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        {/* Profile Information */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Profile Information</SidebarGroupLabel>
          <SidebarGroupContent className="px-4 space-y-4">
            {editingProfile ? (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-4"
              >
                <div>
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    value={profileForm.name || ''}
                    onChange={(e) => setProfileForm({ ...profileForm, name: e.target.value })}
                  />
                </div>
                
                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    value={profileForm.phone || ''}
                    onChange={(e) => setProfileForm({ ...profileForm, phone: e.target.value })}
                  />
                </div>
                
                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    value={profileForm.location || ''}
                    onChange={(e) => setProfileForm({ ...profileForm, location: e.target.value })}
                  />
                </div>
                
                <div>
                  <Label htmlFor="bio">Bio</Label>
                  <Input
                    id="bio"
                    value={profileForm.bio || ''}
                    onChange={(e) => setProfileForm({ ...profileForm, bio: e.target.value })}
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button onClick={handleProfileSave} size="sm" className="flex-1">
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </Button>
                  <Button onClick={handleProfileCancel} size="sm" variant="outline" className="flex-1">
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              </motion.div>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span>{profile.email}</span>
                </div>
                
                {profile.phone && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{profile.phone}</span>
                  </div>
                )}
                
                {profile.location && (
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>{profile.location}</span>
                  </div>
                )}
                
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>Joined {formatDate(profile.joinDate)}</span>
                </div>
                
                <div className="flex items-center gap-2 text-sm">
                  <Activity className="h-4 w-4 text-muted-foreground" />
                  <span>Last active {formatDate(profile.lastLogin)}</span>
                </div>
                
                {profile.bio && (
                  <div className="text-sm text-muted-foreground mt-3 p-3 rounded-lg bg-muted/50">
                    {profile.bio}
                  </div>
                )}
              </div>
            )}
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Preferences */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Preferences</SidebarGroupLabel>
          <SidebarGroupContent className="px-4 space-y-4">
            {/* Theme */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Theme</Label>
              <Select
                value={preferences.theme}
                onValueChange={(value) => handlePreferenceChange('theme', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">
                    <div className="flex items-center gap-2">
                      <Sun className="h-4 w-4" />
                      Light
                    </div>
                  </SelectItem>
                  <SelectItem value="dark">
                    <div className="flex items-center gap-2">
                      <Moon className="h-4 w-4" />
                      Dark
                    </div>
                  </SelectItem>
                  <SelectItem value="system">
                    <div className="flex items-center gap-2">
                      <Monitor className="h-4 w-4" />
                      System
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Language */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Language</Label>
              <Select
                value={preferences.language}
                onValueChange={(value) => handlePreferenceChange('language', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="es">Español</SelectItem>
                  <SelectItem value="fr">Français</SelectItem>
                  <SelectItem value="de">Deutsch</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Time Format */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Time Format</Label>
              <Select
                value={preferences.timeFormat}
                onValueChange={(value) => handlePreferenceChange('timeFormat', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="12h">12-hour (AM/PM)</SelectItem>
                  <SelectItem value="24h">24-hour</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Notifications */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Notifications</SidebarGroupLabel>
          <SidebarGroupContent className="px-4 space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Email notifications</span>
              </div>
              <Switch
                checked={preferences.notifications.email}
                onCheckedChange={(checked) => handleNotificationChange('email', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Smartphone className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Push notifications</span>
              </div>
              <Switch
                checked={preferences.notifications.push}
                onCheckedChange={(checked) => handleNotificationChange('push', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Monitor className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Desktop notifications</span>
              </div>
              <Switch
                checked={preferences.notifications.desktop}
                onCheckedChange={(checked) => handleNotificationChange('desktop', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Workspace updates</span>
              </div>
              <Switch
                checked={preferences.notifications.workspaceUpdates}
                onCheckedChange={(checked) => handleNotificationChange('workspaceUpdates', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">System alerts</span>
              </div>
              <Switch
                checked={preferences.notifications.systemAlerts}
                onCheckedChange={(checked) => handleNotificationChange('systemAlerts', checked)}
              />
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Quick Actions */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Account Actions</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={onPasswordChange}>
                  <Key className="h-4 w-4 mr-2" />
                  Change Password
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={() => onQuickAction?.({ 
                  id: 'export-data', 
                  type: 'button', 
                  title: 'Export Data', 
                  onClick: () => {} 
                })}>
                  <Settings className="h-4 w-4 mr-2" />
                  Export Data
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton 
                  onClick={onAccountDeactivate}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Deactivate Account
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
