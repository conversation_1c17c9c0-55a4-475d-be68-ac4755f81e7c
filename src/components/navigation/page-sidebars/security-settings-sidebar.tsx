'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Shield,
  Key,
  Smartphone,
  Lock,
  Unlock,
  Eye,
  EyeOff,
  AlertTriangle,
  CheckCircle,
  Clock,
  Globe,
  Monitor,
  Wifi,
  Settings,
  Plus,
  Trash2,
  Edit3,
  Download,
  Upload,
  RefreshCw,
  X,
  Check,
  Info,
  ExternalLink,
  QrCode,
  Copy,
  Activity,
  Calendar,
  MapPin,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

import { SettingsSidebarProps } from '@/types/navigation';
import { cn } from '@/lib/utils';

interface SecuritySettings {
  twoFactorEnabled: boolean;
  sessionTimeout: number; // minutes
  passwordExpiry: number; // days
  loginNotifications: boolean;
  deviceTracking: boolean;
  ipWhitelist: string[];
  allowedCountries: string[];
  requireStrongPassword: boolean;
  preventPasswordReuse: boolean;
  maxLoginAttempts: number;
  accountLockoutDuration: number; // minutes
}

interface ActiveSession {
  id: string;
  device: string;
  browser: string;
  location: string;
  ipAddress: string;
  lastActivity: Date;
  isCurrent: boolean;
  isVerified: boolean;
}

interface LoginAttempt {
  id: string;
  timestamp: Date;
  ipAddress: string;
  location: string;
  success: boolean;
  device: string;
  browser: string;
}

interface SecuritySettingsSidebarProps extends SettingsSidebarProps {
  securitySettings?: SecuritySettings;
  activeSessions?: ActiveSession[];
  recentLogins?: LoginAttempt[];
  onSecurityUpdate?: (updates: Partial<SecuritySettings>) => void;
  onSessionRevoke?: (sessionId: string) => void;
  onRevokeAllSessions?: () => void;
  onEnable2FA?: () => void;
  onDisable2FA?: () => void;
  onGenerateBackupCodes?: () => void;
  onDownloadSecurityReport?: () => void;
}

export function SecuritySettingsSidebar({
  securitySettings,
  activeSessions = [],
  recentLogins = [],
  userPermissions,
  onSecurityUpdate,
  onSessionRevoke,
  onRevokeAllSessions,
  onEnable2FA,
  onDisable2FA,
  onGenerateBackupCodes,
  onDownloadSecurityReport,
  onQuickAction,
  className,
  loading = false,
  ...props
}: SecuritySettingsSidebarProps) {
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [newIpAddress, setNewIpAddress] = useState('');

  // Mock data if not provided
  const defaultSettings: SecuritySettings = {
    twoFactorEnabled: true,
    sessionTimeout: 480, // 8 hours
    passwordExpiry: 90,
    loginNotifications: true,
    deviceTracking: true,
    ipWhitelist: ['***********/24', '10.0.0.0/8'],
    allowedCountries: ['US', 'CA', 'GB'],
    requireStrongPassword: true,
    preventPasswordReuse: true,
    maxLoginAttempts: 5,
    accountLockoutDuration: 30,
  };

  const defaultSessions: ActiveSession[] = [
    {
      id: 'session-1',
      device: 'MacBook Pro',
      browser: 'Chrome 120.0',
      location: 'San Francisco, CA',
      ipAddress: '*************',
      lastActivity: new Date(),
      isCurrent: true,
      isVerified: true,
    },
    {
      id: 'session-2',
      device: 'iPhone 15',
      browser: 'Safari Mobile',
      location: 'San Francisco, CA',
      ipAddress: '*************',
      lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),
      isCurrent: false,
      isVerified: true,
    },
    {
      id: 'session-3',
      device: 'Windows PC',
      browser: 'Edge 120.0',
      location: 'New York, NY',
      ipAddress: '************',
      lastActivity: new Date(Date.now() - 24 * 60 * 60 * 1000),
      isCurrent: false,
      isVerified: false,
    },
  ];

  const defaultLogins: LoginAttempt[] = [
    {
      id: 'login-1',
      timestamp: new Date(),
      ipAddress: '*************',
      location: 'San Francisco, CA',
      success: true,
      device: 'MacBook Pro',
      browser: 'Chrome',
    },
    {
      id: 'login-2',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      ipAddress: '*************',
      location: 'San Francisco, CA',
      success: true,
      device: 'iPhone',
      browser: 'Safari',
    },
    {
      id: 'login-3',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
      ipAddress: '************',
      location: 'Unknown',
      success: false,
      device: 'Unknown',
      browser: 'Unknown',
    },
  ];

  const settings = securitySettings || defaultSettings;
  const sessions = activeSessions.length > 0 ? activeSessions : defaultSessions;
  const logins = recentLogins.length > 0 ? recentLogins : defaultLogins;

  const handleSettingChange = (key: string, value: any) => {
    onSecurityUpdate?.({ [key]: value });
  };

  const handleAddIpToWhitelist = () => {
    if (newIpAddress && !settings.ipWhitelist.includes(newIpAddress)) {
      const updatedWhitelist = [...settings.ipWhitelist, newIpAddress];
      onSecurityUpdate?.({ ipWhitelist: updatedWhitelist });
      setNewIpAddress('');
    }
  };

  const handleRemoveIpFromWhitelist = (ip: string) => {
    const updatedWhitelist = settings.ipWhitelist.filter(item => item !== ip);
    onSecurityUpdate?.({ ipWhitelist: updatedWhitelist });
  };

  const getDeviceIcon = (device: string) => {
    if (device.toLowerCase().includes('iphone') || device.toLowerCase().includes('android')) {
      return Smartphone;
    }
    return Monitor;
  };

  const getSecurityScore = () => {
    let score = 0;
    if (settings.twoFactorEnabled) score += 25;
    if (settings.requireStrongPassword) score += 20;
    if (settings.loginNotifications) score += 15;
    if (settings.deviceTracking) score += 15;
    if (settings.ipWhitelist.length > 0) score += 15;
    if (settings.sessionTimeout <= 480) score += 10;
    return Math.min(score, 100);
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const securityScore = getSecurityScore();

  return (
    <Sidebar className={cn('w-80 border-l', className)} {...props}>
      <SidebarHeader className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Settings
          </h2>
          <Button onClick={onDownloadSecurityReport} size="sm" variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Report
          </Button>
        </div>

        {/* Security Score */}
        <div className="p-4 rounded-lg border bg-card">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Security Score</span>
            <Badge variant={securityScore >= 80 ? 'default' : securityScore >= 60 ? 'secondary' : 'destructive'}>
              {securityScore}/100
            </Badge>
          </div>
          <Progress value={securityScore} className="h-2 mb-2" />
          <p className="text-xs text-muted-foreground">
            {securityScore >= 80 ? 'Excellent security' : 
             securityScore >= 60 ? 'Good security' : 'Needs improvement'}
          </p>
        </div>
      </SidebarHeader>

      <SidebarContent>
        {/* Authentication */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Authentication</SidebarGroupLabel>
          <SidebarGroupContent className="px-4 space-y-4">
            {/* Two-Factor Authentication */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Smartphone className="h-4 w-4 text-muted-foreground" />
                <div>
                  <span className="text-sm font-medium">Two-Factor Auth</span>
                  <p className="text-xs text-muted-foreground">
                    {settings.twoFactorEnabled ? 'Enabled' : 'Disabled'}
                  </p>
                </div>
              </div>
              <Switch
                checked={settings.twoFactorEnabled}
                onCheckedChange={(checked) => {
                  if (checked) {
                    onEnable2FA?.();
                  } else {
                    onDisable2FA?.();
                  }
                }}
              />
            </div>

            {settings.twoFactorEnabled && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="ml-6 space-y-2"
              >
                <Button
                  onClick={onGenerateBackupCodes}
                  size="sm"
                  variant="outline"
                  className="w-full"
                >
                  <Key className="h-4 w-4 mr-2" />
                  Generate Backup Codes
                </Button>
                <Button
                  onClick={() => setShowBackupCodes(true)}
                  size="sm"
                  variant="ghost"
                  className="w-full text-xs"
                >
                  <QrCode className="h-4 w-4 mr-2" />
                  View QR Code
                </Button>
              </motion.div>
            )}

            {/* Password Settings */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Strong passwords</span>
                </div>
                <Switch
                  checked={settings.requireStrongPassword}
                  onCheckedChange={(checked) => handleSettingChange('requireStrongPassword', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Prevent password reuse</span>
                </div>
                <Switch
                  checked={settings.preventPasswordReuse}
                  onCheckedChange={(checked) => handleSettingChange('preventPasswordReuse', checked)}
                />
              </div>

              <div>
                <Label className="text-sm font-medium mb-2 block">Password Expiry</Label>
                <Select
                  value={settings.passwordExpiry.toString()}
                  onValueChange={(value) => handleSettingChange('passwordExpiry', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 days</SelectItem>
                    <SelectItem value="60">60 days</SelectItem>
                    <SelectItem value="90">90 days</SelectItem>
                    <SelectItem value="180">180 days</SelectItem>
                    <SelectItem value="365">1 year</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Session Management */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Session Management</SidebarGroupLabel>
          <SidebarGroupContent className="px-4 space-y-4">
            <div>
              <Label className="text-sm font-medium mb-2 block">Session Timeout</Label>
              <Select
                value={settings.sessionTimeout.toString()}
                onValueChange={(value) => handleSettingChange('sessionTimeout', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="60">1 hour</SelectItem>
                  <SelectItem value="240">4 hours</SelectItem>
                  <SelectItem value="480">8 hours</SelectItem>
                  <SelectItem value="720">12 hours</SelectItem>
                  <SelectItem value="1440">24 hours</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Device tracking</span>
              </div>
              <Switch
                checked={settings.deviceTracking}
                onCheckedChange={(checked) => handleSettingChange('deviceTracking', checked)}
              />
            </div>

            <Button
              onClick={onRevokeAllSessions}
              variant="outline"
              size="sm"
              className="w-full"
            >
              <X className="h-4 w-4 mr-2" />
              Revoke All Sessions
            </Button>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Active Sessions */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Active Sessions ({sessions.length})</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {sessions.map((session, index) => {
                const DeviceIcon = getDeviceIcon(session.device);
                
                return (
                  <motion.div
                    key={session.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-3 rounded-lg border bg-card"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <DeviceIcon className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="text-sm font-medium flex items-center gap-2">
                            {session.device}
                            {session.isCurrent && (
                              <Badge variant="secondary" className="text-xs">Current</Badge>
                            )}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {session.browser}
                          </div>
                        </div>
                      </div>
                      {!session.isCurrent && (
                        <Button
                          onClick={() => onSessionRevoke?.(session.id)}
                          size="sm"
                          variant="ghost"
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                    
                    <div className="space-y-1 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        <span>{session.location}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Globe className="h-3 w-3" />
                        <span>{session.ipAddress}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>Last active {formatTimestamp(session.lastActivity)}</span>
                      </div>
                    </div>
                    
                    {!session.isVerified && (
                      <div className="flex items-center gap-1 mt-2 text-xs text-yellow-600">
                        <AlertTriangle className="h-3 w-3" />
                        <span>Unverified location</span>
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Security Notifications */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Security Notifications</SidebarGroupLabel>
          <SidebarGroupContent className="px-4 space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Bell className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Login notifications</span>
              </div>
              <Switch
                checked={settings.loginNotifications}
                onCheckedChange={(checked) => handleSettingChange('loginNotifications', checked)}
              />
            </div>

            <div>
              <Label className="text-sm font-medium mb-2 block">Max Login Attempts</Label>
              <Select
                value={settings.maxLoginAttempts.toString()}
                onValueChange={(value) => handleSettingChange('maxLoginAttempts', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="3">3 attempts</SelectItem>
                  <SelectItem value="5">5 attempts</SelectItem>
                  <SelectItem value="10">10 attempts</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* IP Whitelist */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">IP Whitelist</SidebarGroupLabel>
          <SidebarGroupContent className="px-4 space-y-3">
            <div className="flex gap-2">
              <Input
                placeholder="***********/24"
                value={newIpAddress}
                onChange={(e) => setNewIpAddress(e.target.value)}
                className="flex-1"
              />
              <Button onClick={handleAddIpToWhitelist} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-2 max-h-32 overflow-y-auto">
              {settings.ipWhitelist.map((ip, index) => (
                <motion.div
                  key={ip}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="flex items-center justify-between p-2 rounded border bg-muted/50"
                >
                  <span className="text-sm font-mono">{ip}</span>
                  <Button
                    onClick={() => handleRemoveIpFromWhitelist(ip)}
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </motion.div>
              ))}
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="mx-4" />

        {/* Quick Actions */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Security Actions</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={() => onQuickAction?.({ 
                  id: 'change-password', 
                  type: 'button', 
                  title: 'Change Password', 
                  onClick: () => {} 
                })}>
                  <Key className="h-4 w-4 mr-2" />
                  Change Password
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={() => onQuickAction?.({ 
                  id: 'view-login-history', 
                  type: 'button', 
                  title: 'View Login History', 
                  onClick: () => {} 
                })}>
                  <Activity className="h-4 w-4 mr-2" />
                  Login History
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={onDownloadSecurityReport}>
                  <Download className="h-4 w-4 mr-2" />
                  Security Report
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
