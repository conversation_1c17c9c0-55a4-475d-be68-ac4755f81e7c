'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  HardDrive,
  Play,
  Square,
  Pause,
  RotateCcw,
  Trash2,
  Plus,
  Search,
  Filter,
  Terminal,
  FileText,
  Activity,
  Clock,
  Cpu,
  Network,
  MoreVertical,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  Server,
  Wifi,
  WifiOff,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

import { PageSidebarProps } from '@/types/navigation';
import { ContainerInfo } from '@/types/docker';
import { RemoteContainerInfo, SSHConnectionStatus } from '@/types/ssh-docker';
import { useDocker } from '@/hooks/useDocker';
import { useSSHDocker } from '@/hooks/useSSHDocker';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface EnhancedContainersSidebarProps extends Omit<PageSidebarProps, 'config'> {
  onCreateContainer?: () => void;
  onContainerAction?: (containerId: string, action: string, isRemote?: boolean, connectionId?: string) => void;
}

export function EnhancedContainersSidebar({
  onCreateContainer,
  onContainerAction,
  onQuickAction,
  className,
  ...props
}: EnhancedContainersSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [activeTab, setActiveTab] = useState<'local' | 'remote'>('local');

  // Local Docker hook
  const {
    containers: localContainers,
    loading: localLoading,
    error: localError,
    connected: localConnected,
    refreshContainers: refreshLocal,
    startContainer: startLocalContainer,
    stopContainer: stopLocalContainer,
    removeContainer: removeLocalContainer,
  } = useDocker();

  // SSH Docker hook
  const {
    connections,
    loading: remoteLoading,
    error: remoteError,
    refreshConnections,
    startRemoteContainer,
    stopRemoteContainer,
    removeRemoteContainer,
  } = useSSHDocker();

  // Get all remote containers from all connections
  const [remoteContainers, setRemoteContainers] = useState<Array<RemoteContainerInfo & { connectionId: string }>>([]);

  useEffect(() => {
    const fetchRemoteContainers = async () => {
      const allRemoteContainers: Array<RemoteContainerInfo & { connectionId: string }> = [];
      
      for (const connection of connections) {
        if (connection.connected) {
          try {
            // You would implement a method to get containers for a specific connection
            // const containers = await listRemoteContainers(connection.id);
            // allRemoteContainers.push(...containers.map(c => ({ ...c, connectionId: connection.id })));
          } catch (error) {
            console.error(`Failed to fetch containers for ${connection.id}:`, error);
          }
        }
      }
      
      setRemoteContainers(allRemoteContainers);
    };

    if (activeTab === 'remote') {
      fetchRemoteContainers();
    }
  }, [connections, activeTab]);

  // Filter containers based on search and status
  const filteredLocalContainers = localContainers.filter(container => {
    const matchesSearch = container.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         container.image.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || container.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const filteredRemoteContainers = remoteContainers.filter(container => {
    const matchesSearch = container.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         container.image.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || container.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleContainerAction = async (
    containerId: string, 
    action: string, 
    isRemote: boolean = false, 
    connectionId?: string
  ) => {
    try {
      if (isRemote && connectionId) {
        switch (action) {
          case 'start':
            await startRemoteContainer(connectionId, containerId);
            break;
          case 'stop':
            await stopRemoteContainer(connectionId, containerId);
            break;
          case 'remove':
            await removeRemoteContainer(connectionId, containerId);
            break;
          default:
            throw new Error(`Unknown remote action: ${action}`);
        }
        toast.success(`Remote container ${action} successful`);
      } else {
        switch (action) {
          case 'start':
            await startLocalContainer(containerId);
            break;
          case 'stop':
            await stopLocalContainer(containerId);
            break;
          case 'remove':
            await removeLocalContainer(containerId);
            break;
          default:
            throw new Error(`Unknown local action: ${action}`);
        }
        toast.success(`Container ${action} successful`);
        refreshLocal();
      }
      
      onContainerAction?.(containerId, action, isRemote, connectionId);
    } catch (error) {
      toast.error(`Failed to ${action} container: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return CheckCircle;
      case 'stopped': return XCircle;
      case 'starting': return Loader2;
      case 'stopping': return AlertTriangle;
      default: return XCircle;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'stopped': return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      case 'starting': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'stopping': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const containerStats = {
    local: {
      total: localContainers.length,
      running: localContainers.filter(c => c.status === 'running').length,
      stopped: localContainers.filter(c => c.status === 'stopped').length,
    },
    remote: {
      total: remoteContainers.length,
      running: remoteContainers.filter(c => c.status === 'running').length,
      stopped: remoteContainers.filter(c => c.status === 'stopped').length,
    }
  };

  const renderContainer = (container: ContainerInfo | (RemoteContainerInfo & { connectionId: string }), index: number, isRemote: boolean = false) => {
    const StatusIcon = getStatusIcon(container.status);
    
    return (
      <motion.div
        key={container.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ delay: index * 0.05 }}
        className="mb-2"
      >
        <SidebarMenuItem>
          <div className="group relative p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-center gap-2">
                <StatusIcon className={cn(
                  'h-4 w-4',
                  container.status === 'starting' && 'animate-spin'
                )} />
                <div>
                  <div className="font-medium text-sm">{container.name}</div>
                  <div className="text-xs text-muted-foreground">{container.image}</div>
                  {isRemote && 'connectionId' in container && (
                    <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                      {container.remoteHost}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-1">
                {isRemote && (
                  <Server className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                )}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <MoreVertical className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {container.status === 'running' ? (
                      <DropdownMenuItem onClick={() => 
                        handleContainerAction(
                          container.id, 
                          'stop', 
                          isRemote, 
                          'connectionId' in container ? container.connectionId : undefined
                        )
                      }>
                        <Square className="h-4 w-4 mr-2" />
                        Stop
                      </DropdownMenuItem>
                    ) : (
                      <DropdownMenuItem onClick={() => 
                        handleContainerAction(
                          container.id, 
                          'start', 
                          isRemote, 
                          'connectionId' in container ? container.connectionId : undefined
                        )
                      }>
                        <Play className="h-4 w-4 mr-2" />
                        Start
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onContainerAction?.(container.id, 'logs', isRemote, 'connectionId' in container ? container.connectionId : undefined)}>
                      <FileText className="h-4 w-4 mr-2" />
                      View Logs
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onContainerAction?.(container.id, 'terminal', isRemote, 'connectionId' in container ? container.connectionId : undefined)}>
                      <Terminal className="h-4 w-4 mr-2" />
                      Terminal
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => 
                        handleContainerAction(
                          container.id, 
                          'remove', 
                          isRemote, 
                          'connectionId' in container ? container.connectionId : undefined
                        )
                      }
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            <div className="flex items-center gap-2 mb-2">
              <Badge className={cn('text-xs', getStatusColor(container.status))}>
                {container.status}
              </Badge>
              {container.status === 'running' && container.cpu !== undefined && (
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Cpu className="h-3 w-3" />
                    {container.cpu}%
                  </span>
                  {container.memory && <span>{container.memory}MB</span>}
                </div>
              )}
            </div>

            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {container.created instanceof Date ? container.created.toLocaleDateString() : container.created}
              </span>
              <div className="flex items-center gap-1">
                <Network className="h-3 w-3" />
                <span>{Object.keys(container.ports).length} ports</span>
              </div>
            </div>
          </div>
        </SidebarMenuItem>
      </motion.div>
    );
  };

  return (
    <Sidebar className={cn('w-80 border-l', className)} {...props}>
      <SidebarHeader className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Containers</h2>
          <Button onClick={onCreateContainer} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Create
          </Button>
        </div>

        {/* Tabs for Local vs Remote */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'local' | 'remote')} className="mb-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="local" className="flex items-center gap-2">
              <HardDrive className="h-4 w-4" />
              Local
            </TabsTrigger>
            <TabsTrigger value="remote" className="flex items-center gap-2">
              <Server className="h-4 w-4" />
              Remote
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Container Stats */}
        <div className="grid grid-cols-2 gap-2 mb-4">
          <div className="p-2 rounded-lg border bg-card text-center">
            <div className="text-lg font-semibold text-green-600">
              {containerStats[activeTab].running}
            </div>
            <div className="text-xs text-muted-foreground">Running</div>
          </div>
          <div className="p-2 rounded-lg border bg-card text-center">
            <div className="text-lg font-semibold text-red-600">
              {containerStats[activeTab].stopped}
            </div>
            <div className="text-xs text-muted-foreground">Stopped</div>
          </div>
        </div>

        {/* Connection Status */}
        {activeTab === 'local' && (
          <div className="flex items-center gap-2 mb-4 p-2 rounded-lg bg-muted/50">
            {localConnected ? (
              <>
                <Wifi className="h-4 w-4 text-green-600" />
                <span className="text-sm text-green-600 font-medium">Docker Connected</span>
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-600 font-medium">Docker Disconnected</span>
              </>
            )}
          </div>
        )}

        {activeTab === 'remote' && (
          <div className="mb-4 p-2 rounded-lg bg-muted/50">
            <div className="text-sm font-medium mb-1">SSH Connections</div>
            <div className="text-xs text-muted-foreground">
              {connections.filter(c => c.connected).length} of {connections.length} connected
            </div>
          </div>
        )}

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search containers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Filters */}
        <div className="flex gap-2 mb-4">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="running">Running</SelectItem>
              <SelectItem value="stopped">Stopped</SelectItem>
              <SelectItem value="starting">Starting</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className="px-4">
            {activeTab === 'local' ? 'Local' : 'Remote'} Containers ({
              activeTab === 'local' ? filteredLocalContainers.length : filteredRemoteContainers.length
            })
          </SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <AnimatePresence mode="popLayout">
              {activeTab === 'local' ? (
                filteredLocalContainers.map((container, index) => 
                  renderContainer(container, index, false)
                )
              ) : (
                filteredRemoteContainers.map((container, index) => 
                  renderContainer(container, index, true)
                )
              )}
            </AnimatePresence>

            {/* Empty State */}
            {((activeTab === 'local' && filteredLocalContainers.length === 0) ||
              (activeTab === 'remote' && filteredRemoteContainers.length === 0)) && 
              !localLoading && !remoteLoading && (
              <div className="text-center py-8 text-muted-foreground">
                <HardDrive className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No containers found</p>
                {searchQuery && (
                  <p className="text-xs mt-1">Try adjusting your search or filters</p>
                )}
                {activeTab === 'remote' && connections.length === 0 && (
                  <p className="text-xs mt-1">No SSH connections configured</p>
                )}
              </div>
            )}

            {/* Loading State */}
            {(localLoading || remoteLoading) && (
              <div className="text-center py-8">
                <Loader2 className="h-6 w-6 mx-auto animate-spin mb-2" />
                <p className="text-sm text-muted-foreground">Loading containers...</p>
              </div>
            )}

            {/* Error State */}
            {(localError || remoteError) && (
              <div className="text-center py-8 text-red-600">
                <AlertTriangle className="h-6 w-6 mx-auto mb-2" />
                <p className="text-sm">{localError || remoteError}</p>
              </div>
            )}
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="my-4" />

        <SidebarGroup>
          <SidebarGroupLabel className="px-4">Quick Actions</SidebarGroupLabel>
          <SidebarGroupContent className="px-2">
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={() => onQuickAction?.({ 
                  id: 'bulk-start', 
                  type: 'button', 
                  title: `Start All Stopped (${activeTab})`, 
                  onClick: () => {} 
                })}>
                  <Play className="h-4 w-4 mr-2" />
                  Start All Stopped
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={() => onQuickAction?.({ 
                  id: 'bulk-stop', 
                  type: 'button', 
                  title: `Stop All Running (${activeTab})`, 
                  onClick: () => {} 
                })}>
                  <Square className="h-4 w-4 mr-2" />
                  Stop All Running
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton onClick={() => activeTab === 'local' ? refreshLocal() : refreshConnections()}>
                  <Activity className="h-4 w-4 mr-2" />
                  Refresh {activeTab === 'local' ? 'Local' : 'Remote'}
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}