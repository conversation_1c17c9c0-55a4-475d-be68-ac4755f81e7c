/**
 * Navigation Components Index
 * Centralized exports for all navigation components and utilities
 */

// Main navigation components
export { EnhancedAppSidebar } from './enhanced-app-sidebar';
export { ResponsiveSidebarLayout, useResponsiveSidebarLayout } from './responsive-sidebar-layout';

// Page-specific sidebars
export {
  WorkspacesSidebar,
  MonitoringSidebar,
  ContainersSidebar,
  SettingsSidebar,
} from './page-sidebars';

// Animation and styling components
export {
  AnimatedSidebarWrapper,
  AnimatedSidebarItem,
  AnimatedSidebarGroup,
  FloatingActionButton,
} from './animated-sidebar-wrapper';

// Accessibility components
export {
  AccessibleSidebar,
  AccessibleNavigationItem,
} from './accessible-sidebar';

// Role-based access control
export {
  RoleBasedNavigation,
  useRoleBasedNavigation,
  withRoleAccess,
  PermissionGuard,
  RoleBadge,
  PermissionIndicator,
} from './role-based-navigation';

// Context and hooks
export {
  NavigationProvider,
  useNavigation,
  useSidebarState,
  useNavigationItems,
  usePageSidebar,
  useResponsiveSidebar,
} from '@/contexts/navigation-context';

export {
  useNavigationUtils,
  useNavigationSearch,
  useNavigationKeyboard,
  useNavigationAnalytics,
  useNavigationPersistence,
  useNavigationAccessibility,
} from '@/hooks/useNavigationUtils';

// Types
export type {
  NavigationItem,
  NavigationLink,
  NavigationButton,
  NavigationGroup,
  NavigationDropdown,
  NavigationSeparator,
  SidebarConfig,
  PageSidebarConfig,
  MainSidebarConfig,
  SidebarState,
  SidebarActions,
  NavigationContextValue,
  BreadcrumbItem,
  BreadcrumbConfig,
  SidebarAnimationConfig,
  SidebarThemeConfig,
  DashboardPageConfig,
  DashboardPageId,
  UserRole,
  Permission,
  RolePermissions,
  ResponsiveConfig,
  KeyboardNavigationConfig,
} from '@/types/navigation';

// Utilities
export {
  NavigationStateManager,
  BreadcrumbGenerator,
  NavigationItemUtils,
  PageConfigUtils,
  defaultDashboardPages,
  STORAGE_KEYS,
} from '@/lib/navigation-utils';
