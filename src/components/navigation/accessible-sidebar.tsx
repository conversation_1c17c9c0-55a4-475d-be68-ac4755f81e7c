'use client';

import React, { useRef, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  SkipForward, 
  Volume2, 
  VolumeX, 
  Eye, 
  EyeOff,
  Keyboard,
  MousePointer,
  Focus,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from '@/components/ui/tooltip';

import { useNavigation } from '@/contexts/navigation-context';
import { useNavigationAccessibility, useNavigationKeyboard } from '@/hooks/useNavigationUtils';
import { NavigationItem, KeyboardNavigationConfig } from '@/types/navigation';
import { cn } from '@/lib/utils';

// Default keyboard navigation configuration
const defaultKeyboardConfig: KeyboardNavigationConfig = {
  enabled: true,
  shortcuts: {
    'Ctrl+B': 'Toggle sidebar',
    'Ctrl+Shift+D': 'Go to dashboard',
    'Ctrl+Shift+W': 'Go to workspaces',
    'Ctrl+Shift+M': 'Go to monitoring',
    'Ctrl+Shift+C': 'Go to containers',
    'Ctrl+Shift+S': 'Go to settings',
    'ArrowUp': 'Previous item',
    'ArrowDown': 'Next item',
    'Enter': 'Activate item',
    'Escape': 'Close sidebar',
    'Tab': 'Next focusable',
    'Shift+Tab': 'Previous focusable',
  },
  focusManagement: true,
  skipLinks: true,
  announcements: true,
};

interface AccessibleSidebarProps {
  children: React.ReactNode;
  keyboardConfig?: Partial<KeyboardNavigationConfig>;
  ariaLabel?: string;
  ariaDescription?: string;
  role?: string;
  className?: string;
}

export function AccessibleSidebar({
  children,
  keyboardConfig = {},
  ariaLabel = 'Main navigation',
  ariaDescription = 'Navigate through the application sections',
  role = 'navigation',
  className,
}: AccessibleSidebarProps) {
  const sidebarRef = useRef<HTMLElement>(null);
  const { sidebarState, sidebarActions } = useNavigation();
  const { announceNavigation, focusFirstItem, skipToContent } = useNavigationAccessibility();
  const { handleKeyDown } = useNavigationKeyboard();
  
  const config = { ...defaultKeyboardConfig, ...keyboardConfig };

  // Focus management
  const focusableElements = useRef<HTMLElement[]>([]);
  const currentFocusIndex = useRef(0);

  // Update focusable elements
  const updateFocusableElements = useCallback(() => {
    if (!sidebarRef.current) return;
    
    const elements = sidebarRef.current.querySelectorAll(
      'button:not([disabled]), a[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;
    
    focusableElements.current = Array.from(elements);
  }, []);

  // Keyboard navigation handler
  const handleSidebarKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (!config.enabled) return;

    const { key, ctrlKey, metaKey, shiftKey } = event;
    const isModifier = ctrlKey || metaKey;

    // Handle global shortcuts
    if (isModifier) {
      handleKeyDown(event.nativeEvent);
      return;
    }

    // Handle navigation within sidebar
    switch (key) {
      case 'ArrowUp':
        event.preventDefault();
        navigateFocus(-1);
        break;
      case 'ArrowDown':
        event.preventDefault();
        navigateFocus(1);
        break;
      case 'Home':
        event.preventDefault();
        focusElement(0);
        break;
      case 'End':
        event.preventDefault();
        focusElement(focusableElements.current.length - 1);
        break;
      case 'Escape':
        event.preventDefault();
        if (sidebarState.isOpen) {
          sidebarActions.close();
          announceNavigation('Sidebar closed');
        }
        break;
    }
  }, [config.enabled, handleKeyDown, sidebarState.isOpen, sidebarActions, announceNavigation]);

  // Navigate focus between elements
  const navigateFocus = useCallback((direction: number) => {
    updateFocusableElements();
    
    if (focusableElements.current.length === 0) return;
    
    currentFocusIndex.current = Math.max(
      0,
      Math.min(
        focusableElements.current.length - 1,
        currentFocusIndex.current + direction
      )
    );
    
    focusElement(currentFocusIndex.current);
  }, [updateFocusableElements]);

  // Focus specific element
  const focusElement = useCallback((index: number) => {
    const element = focusableElements.current[index];
    if (element) {
      element.focus();
      currentFocusIndex.current = index;
      
      // Announce focused element
      const label = element.getAttribute('aria-label') || 
                   element.textContent || 
                   'Navigation item';
      announceNavigation(`Focused: ${label}`);
    }
  }, [announceNavigation]);

  // Update focusable elements when sidebar content changes
  useEffect(() => {
    updateFocusableElements();
  }, [children, updateFocusableElements]);

  // Focus first item when sidebar opens
  useEffect(() => {
    if (sidebarState.isOpen && config.focusManagement) {
      setTimeout(() => {
        focusFirstItem();
        updateFocusableElements();
      }, 100);
    }
  }, [sidebarState.isOpen, config.focusManagement, focusFirstItem, updateFocusableElements]);

  // Announce sidebar state changes
  useEffect(() => {
    if (config.announcements) {
      const state = sidebarState.isOpen ? 'opened' : 'closed';
      const collapsed = sidebarState.isCollapsed ? ' and collapsed' : '';
      announceNavigation(`Sidebar ${state}${collapsed}`);
    }
  }, [sidebarState.isOpen, sidebarState.isCollapsed, config.announcements, announceNavigation]);

  return (
    <>
      {/* Skip Links */}
      {config.skipLinks && (
        <div className="sr-only focus-within:not-sr-only">
          <Button
            variant="outline"
            size="sm"
            onClick={skipToContent}
            className="absolute top-2 left-2 z-50 bg-background"
          >
            <SkipForward className="h-4 w-4 mr-2" />
            Skip to main content
          </Button>
        </div>
      )}

      {/* Accessible Sidebar */}
      <nav
        ref={sidebarRef}
        role={role}
        aria-label={ariaLabel}
        aria-description={ariaDescription}
        aria-expanded={sidebarState.isOpen}
        aria-hidden={!sidebarState.isOpen}
        onKeyDown={handleSidebarKeyDown}
        className={cn(
          'focus-within:outline-none',
          className
        )}
        tabIndex={-1}
      >
        {/* Screen Reader Status */}
        <div
          id="sidebar-status"
          className="sr-only"
          aria-live="polite"
          aria-atomic="true"
        >
          Sidebar is {sidebarState.isOpen ? 'open' : 'closed'}
          {sidebarState.isCollapsed && ' and collapsed'}
        </div>

        {/* Keyboard Shortcuts Help */}
        {config.enabled && (
          <div className="sr-only">
            <h3>Keyboard shortcuts:</h3>
            <ul>
              {Object.entries(config.shortcuts).map(([key, description]) => (
                <li key={key}>
                  {key}: {description}
                </li>
              ))}
            </ul>
          </div>
        )}

        {children}
      </nav>

      {/* Accessibility Controls */}
      <AccessibilityControls keyboardConfig={config} />
    </>
  );
}

// Accessibility controls component
interface AccessibilityControlsProps {
  keyboardConfig: KeyboardNavigationConfig;
}

function AccessibilityControls({ keyboardConfig }: AccessibilityControlsProps) {
  const [showControls, setShowControls] = React.useState(false);
  const [announcements, setAnnouncements] = React.useState(true);
  const [highContrast, setHighContrast] = React.useState(false);
  const [reducedMotion, setReducedMotion] = React.useState(false);

  // Toggle high contrast mode
  const toggleHighContrast = useCallback(() => {
    setHighContrast(prev => {
      const newValue = !prev;
      document.documentElement.classList.toggle('high-contrast', newValue);
      return newValue;
    });
  }, []);

  // Toggle reduced motion
  const toggleReducedMotion = useCallback(() => {
    setReducedMotion(prev => {
      const newValue = !prev;
      document.documentElement.classList.toggle('reduce-motion', newValue);
      return newValue;
    });
  }, []);

  // Toggle announcements
  const toggleAnnouncements = useCallback(() => {
    setAnnouncements(prev => !prev);
  }, []);

  if (!showControls) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowControls(true)}
              className="fixed bottom-4 left-4 z-50 bg-background/80 backdrop-blur-sm border"
              aria-label="Show accessibility controls"
            >
              <Focus className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Accessibility Controls</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="fixed bottom-4 left-4 z-50 p-4 bg-background/95 backdrop-blur-md border rounded-lg shadow-lg min-w-64"
    >
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold">Accessibility</h3>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setShowControls(false)}
          className="h-6 w-6"
          aria-label="Hide accessibility controls"
        >
          ×
        </Button>
      </div>

      <div className="space-y-3">
        {/* High Contrast */}
        <div className="flex items-center justify-between">
          <label htmlFor="high-contrast" className="text-sm">
            High Contrast
          </label>
          <Button
            id="high-contrast"
            variant={highContrast ? 'default' : 'outline'}
            size="sm"
            onClick={toggleHighContrast}
            aria-pressed={highContrast}
          >
            {highContrast ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
          </Button>
        </div>

        {/* Reduced Motion */}
        <div className="flex items-center justify-between">
          <label htmlFor="reduced-motion" className="text-sm">
            Reduced Motion
          </label>
          <Button
            id="reduced-motion"
            variant={reducedMotion ? 'default' : 'outline'}
            size="sm"
            onClick={toggleReducedMotion}
            aria-pressed={reducedMotion}
          >
            {reducedMotion ? <MousePointer className="h-4 w-4" /> : <Keyboard className="h-4 w-4" />}
          </Button>
        </div>

        {/* Announcements */}
        <div className="flex items-center justify-between">
          <label htmlFor="announcements" className="text-sm">
            Screen Reader
          </label>
          <Button
            id="announcements"
            variant={announcements ? 'default' : 'outline'}
            size="sm"
            onClick={toggleAnnouncements}
            aria-pressed={announcements}
          >
            {announcements ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
          </Button>
        </div>

        {/* Keyboard Shortcuts */}
        {keyboardConfig.enabled && (
          <div className="pt-2 border-t">
            <p className="text-xs text-muted-foreground mb-2">Keyboard Shortcuts:</p>
            <div className="space-y-1">
              {Object.entries(keyboardConfig.shortcuts).slice(0, 3).map(([key, description]) => (
                <div key={key} className="flex justify-between text-xs">
                  <Badge variant="outline" className="text-xs px-1 py-0">
                    {key}
                  </Badge>
                  <span className="text-muted-foreground">{description}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
}

// Accessible navigation item wrapper
interface AccessibleNavigationItemProps {
  item: NavigationItem;
  isActive?: boolean;
  isDisabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

export function AccessibleNavigationItem({
  item,
  isActive = false,
  isDisabled = false,
  children,
  onClick,
  className,
}: AccessibleNavigationItemProps) {
  const { announceNavigation } = useNavigationAccessibility();

  const handleClick = useCallback(() => {
    if (!isDisabled) {
      onClick?.();
      announceNavigation(`Navigated to ${item.title}`);
    }
  }, [isDisabled, onClick, item.title, announceNavigation]);

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleClick();
    }
  }, [handleClick]);

  return (
    <div
      role="menuitem"
      tabIndex={isDisabled ? -1 : 0}
      aria-label={item.title}
      aria-description={item.description}
      aria-current={isActive ? 'page' : undefined}
      aria-disabled={isDisabled}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      className={cn(
        'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-md',
        isDisabled && 'opacity-50 cursor-not-allowed',
        className
      )}
    >
      {children}
    </div>
  );
}
