'use client';

import React, { useMemo } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { useNavigation } from '@/contexts/navigation-context';
import {
  NavigationItem,
  UserRole,
  Permission,
  RolePermissions,
  NavigationItemFilter,
} from '@/types/navigation';

// Default role permissions configuration
const defaultRolePermissions: RolePermissions[] = [
  {
    role: 'admin',
    permissions: [
      { action: 'read', resource: '*' },
      { action: 'write', resource: '*' },
      { action: 'delete', resource: '*' },
      { action: 'manage', resource: 'users' },
      { action: 'manage', resource: 'system' },
      { action: 'manage', resource: 'billing' },
    ],
  },
  {
    role: 'moderator',
    permissions: [
      { action: 'read', resource: '*' },
      { action: 'write', resource: 'workspaces' },
      { action: 'write', resource: 'containers' },
      { action: 'manage', resource: 'users', conditions: { scope: 'team' } },
    ],
    inherits: ['user'],
  },
  {
    role: 'developer',
    permissions: [
      { action: 'read', resource: '*' },
      { action: 'write', resource: 'workspaces' },
      { action: 'write', resource: 'containers' },
      { action: 'write', resource: 'images' },
      { action: 'read', resource: 'monitoring' },
    ],
    inherits: ['user'],
  },
  {
    role: 'user',
    permissions: [
      { action: 'read', resource: 'dashboard' },
      { action: 'read', resource: 'workspaces' },
      { action: 'write', resource: 'workspaces', conditions: { owner: true } },
      { action: 'read', resource: 'containers' },
      { action: 'read', resource: 'images' },
      { action: 'read', resource: 'settings' },
      { action: 'write', resource: 'settings', conditions: { scope: 'profile' } },
    ],
  },
  {
    role: 'viewer',
    permissions: [
      { action: 'read', resource: 'dashboard' },
      { action: 'read', resource: 'workspaces' },
      { action: 'read', resource: 'monitoring' },
    ],
  },
];

interface RoleBasedNavigationProps {
  children: React.ReactNode;
  items: NavigationItem[];
  rolePermissions?: RolePermissions[];
  fallbackComponent?: React.ReactNode;
  showPermissionDenied?: boolean;
}

export function RoleBasedNavigation({
  children,
  items,
  rolePermissions = defaultRolePermissions,
  fallbackComponent,
  showPermissionDenied = false,
}: RoleBasedNavigationProps) {
  const { user, profile } = useAuth();
  const { checkPermission, checkRole } = useNavigation();

  // Get user role from profile or default to 'viewer'
  const userRole: UserRole = (profile?.role as UserRole) || 'viewer';

  // Get user permissions based on role
  const userPermissions = useMemo(() => {
    const roleConfig = rolePermissions.find(rp => rp.role === userRole);
    if (!roleConfig) return [];

    let permissions = [...roleConfig.permissions];

    // Inherit permissions from parent roles
    if (roleConfig.inherits) {
      for (const inheritedRole of roleConfig.inherits) {
        const inheritedConfig = rolePermissions.find(rp => rp.role === inheritedRole);
        if (inheritedConfig) {
          permissions = [...permissions, ...inheritedConfig.permissions];
        }
      }
    }

    return permissions;
  }, [userRole, rolePermissions]);

  // Permission checking function
  const hasPermission = (permission: Permission): boolean => {
    return userPermissions.some(p => {
      // Check action and resource match
      const actionMatch = p.action === '*' || p.action === permission.action;
      const resourceMatch = p.resource === '*' || p.resource === permission.resource;

      if (!actionMatch || !resourceMatch) return false;

      // Check conditions if present
      if (p.conditions && permission.conditions) {
        return Object.entries(p.conditions).every(([key, value]) => {
          return permission.conditions?.[key] === value;
        });
      }

      return true;
    });
  };

  // Role checking function
  const hasRole = (roles: UserRole[]): boolean => {
    return roles.includes(userRole);
  };

  // Filter navigation items based on permissions
  const filteredItems = useMemo(() => {
    const filterItem = (item: NavigationItem): NavigationItem | null => {
      // Check role requirements
      if (item.requiredRoles && !hasRole(item.requiredRoles)) {
        return null;
      }

      // Check permission requirements
      if (item.requiredPermissions) {
        const hasAllPermissions = item.requiredPermissions.every(hasPermission);
        if (!hasAllPermissions) {
          return null;
        }
      }

      // Filter nested items for groups and dropdowns
      if (item.type === 'group' || item.type === 'dropdown') {
        const filteredNestedItems = item.items
          .map(filterItem)
          .filter((item): item is NavigationItem => item !== null);

        // If no nested items are visible, hide the group/dropdown
        if (filteredNestedItems.length === 0) {
          return null;
        }

        return {
          ...item,
          items: filteredNestedItems,
        };
      }

      return item;
    };

    return items
      .map(filterItem)
      .filter((item): item is NavigationItem => item !== null);
  }, [items, userRole, userPermissions]);

  // If user has no access to any items
  if (filteredItems.length === 0) {
    if (showPermissionDenied) {
      return (
        <div className="p-4 text-center text-muted-foreground">
          <div className="mb-2">🔒</div>
          <p className="text-sm">You don't have permission to access this section.</p>
          <p className="text-xs mt-1">Contact your administrator for access.</p>
        </div>
      );
    }
    return fallbackComponent || null;
  }

  return (
    <RoleBasedNavigationProvider
      userRole={userRole}
      userPermissions={userPermissions}
      hasPermission={hasPermission}
      hasRole={hasRole}
      filteredItems={filteredItems}
    >
      {children}
    </RoleBasedNavigationProvider>
  );
}

// Context for role-based navigation
interface RoleBasedNavigationContextValue {
  userRole: UserRole;
  userPermissions: Permission[];
  hasPermission: (permission: Permission) => boolean;
  hasRole: (roles: UserRole[]) => boolean;
  filteredItems: NavigationItem[];
}

const RoleBasedNavigationContext = React.createContext<RoleBasedNavigationContextValue | null>(null);

interface RoleBasedNavigationProviderProps {
  children: React.ReactNode;
  userRole: UserRole;
  userPermissions: Permission[];
  hasPermission: (permission: Permission) => boolean;
  hasRole: (roles: UserRole[]) => boolean;
  filteredItems: NavigationItem[];
}

function RoleBasedNavigationProvider({
  children,
  userRole,
  userPermissions,
  hasPermission,
  hasRole,
  filteredItems,
}: RoleBasedNavigationProviderProps) {
  const value: RoleBasedNavigationContextValue = {
    userRole,
    userPermissions,
    hasPermission,
    hasRole,
    filteredItems,
  };

  return (
    <RoleBasedNavigationContext.Provider value={value}>
      {children}
    </RoleBasedNavigationContext.Provider>
  );
}

// Hook to use role-based navigation context
export function useRoleBasedNavigation(): RoleBasedNavigationContextValue {
  const context = React.useContext(RoleBasedNavigationContext);
  if (!context) {
    throw new Error('useRoleBasedNavigation must be used within a RoleBasedNavigationProvider');
  }
  return context;
}

// Higher-order component for role-based access
interface WithRoleAccessProps {
  requiredRoles?: UserRole[];
  requiredPermissions?: Permission[];
  fallback?: React.ReactNode;
  showDenied?: boolean;
}

export function withRoleAccess<P extends object>(
  Component: React.ComponentType<P>,
  accessConfig: WithRoleAccessProps
) {
  return function RoleAccessComponent(props: P) {
    const { hasRole, hasPermission } = useRoleBasedNavigation();

    // Check role access
    if (accessConfig.requiredRoles && !hasRole(accessConfig.requiredRoles)) {
      if (accessConfig.showDenied) {
        return (
          <div className="p-4 text-center text-muted-foreground">
            <p className="text-sm">Insufficient role permissions</p>
          </div>
        );
      }
      return accessConfig.fallback || null;
    }

    // Check permission access
    if (accessConfig.requiredPermissions) {
      const hasAllPermissions = accessConfig.requiredPermissions.every(hasPermission);
      if (!hasAllPermissions) {
        if (accessConfig.showDenied) {
          return (
            <div className="p-4 text-center text-muted-foreground">
              <p className="text-sm">Insufficient permissions</p>
            </div>
          );
        }
        return accessConfig.fallback || null;
      }
    }

    return <Component {...props} />;
  };
}

// Permission guard component
interface PermissionGuardProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
  requiredPermissions?: Permission[];
  fallback?: React.ReactNode;
  showDenied?: boolean;
}

export function PermissionGuard({
  children,
  requiredRoles,
  requiredPermissions,
  fallback,
  showDenied = false,
}: PermissionGuardProps) {
  const { hasRole, hasPermission } = useRoleBasedNavigation();

  // Check role access
  if (requiredRoles && !hasRole(requiredRoles)) {
    if (showDenied) {
      return (
        <div className="p-4 text-center text-muted-foreground">
          <p className="text-sm">Role access required: {requiredRoles.join(', ')}</p>
        </div>
      );
    }
    return fallback || null;
  }

  // Check permission access
  if (requiredPermissions) {
    const hasAllPermissions = requiredPermissions.every(hasPermission);
    if (!hasAllPermissions) {
      if (showDenied) {
        return (
          <div className="p-4 text-center text-muted-foreground">
            <p className="text-sm">Additional permissions required</p>
          </div>
        );
      }
      return fallback || null;
    }
  }

  return <>{children}</>;
}

// Role badge component
interface RoleBadgeProps {
  role?: UserRole;
  className?: string;
}

export function RoleBadge({ role, className }: RoleBadgeProps) {
  const { userRole } = useRoleBasedNavigation();
  const displayRole = role || userRole;

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'moderator': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'developer': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'user': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'viewer': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(displayRole)} ${className}`}>
      {displayRole}
    </span>
  );
}

// Permission indicator component
interface PermissionIndicatorProps {
  permission: Permission;
  className?: string;
}

export function PermissionIndicator({ permission, className }: PermissionIndicatorProps) {
  const { hasPermission } = useRoleBasedNavigation();
  const hasAccess = hasPermission(permission);

  return (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
        hasAccess
          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
          : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      } ${className}`}
      title={`${permission.action} ${permission.resource}`}
    >
      {hasAccess ? '✓' : '✗'} {permission.action}
    </span>
  );
}
