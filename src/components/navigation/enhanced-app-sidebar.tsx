'use client';

import * as React from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Cloud, 
  Monitor, 
  Settings, 
  Activity, 
  HardDrive, 
  Cpu, 
  BarChart3, 
  LayoutDashboard,
  ChevronDown,
  Search,
  Pin,
  PinOff,
  MoreHorizontal,
  Plus,
  Filter,
} from 'lucide-react';

import { NavUser } from '@/components/nav-user';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from '@/components/ui/sidebar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';

import { useNavigation } from '@/contexts/navigation-context';
import { useNavigationUtils, useNavigationSearch, useNavigationAnalytics } from '@/hooks/useNavigationUtils';
import {
  NavigationItem,
  NavigationLink,
  NavigationButton,
  NavigationGroup,
  NavigationDropdown,
  MainSidebarProps,
} from '@/types/navigation';
import { cn } from '@/lib/utils';

// Default navigation items for Omnispace
const defaultNavigationItems: NavigationItem[] = [
  {
    id: 'overview',
    type: 'link',
    title: 'Overview',
    href: '/dashboard',
    icon: LayoutDashboard,
    description: 'Dashboard overview and statistics',
  },
  {
    id: 'workspaces',
    type: 'group',
    title: 'Workspaces',
    icon: Monitor,
    description: 'Manage your development environments',
    collapsible: true,
    defaultExpanded: true,
    items: [
      {
        id: 'workspaces-overview',
        type: 'link',
        title: 'All Workspaces',
        href: '/dashboard/workspaces',
        description: 'View all workspaces',
      },
      {
        id: 'workspaces-create',
        type: 'link',
        title: 'Create Workspace',
        href: '/dashboard/workspaces/create',
        description: 'Create a new workspace',
      },
    ],
  },
  {
    id: 'monitoring',
    type: 'link',
    title: 'Monitoring',
    href: '/dashboard/monitoring',
    icon: BarChart3,
    description: 'System monitoring and analytics',
  },
  {
    id: 'containers',
    type: 'link',
    title: 'Containers',
    href: '/dashboard/containers',
    icon: HardDrive,
    description: 'Container management',
  },
  {
    id: 'images',
    type: 'link',
    title: 'Images',
    href: '/dashboard/images',
    icon: Cloud,
    description: 'Container images and templates',
  },
  {
    id: 'separator-1',
    type: 'separator',
  } as NavigationItem,
  {
    id: 'settings',
    type: 'group',
    title: 'Settings',
    icon: Settings,
    description: 'Application settings and preferences',
    collapsible: true,
    items: [
      {
        id: 'settings-profile',
        type: 'link',
        title: 'Profile',
        href: '/dashboard/settings/profile',
        description: 'User profile settings',
      },
      {
        id: 'settings-security',
        type: 'link',
        title: 'Security',
        href: '/dashboard/settings/security',
        description: 'Security and authentication',
        requiredRoles: ['admin', 'user'],
      },
    ],
  },
];

// Navigation item renderer component
interface NavigationItemRendererProps {
  item: NavigationItem;
  level?: number;
  isActive?: boolean;
  onNavigate?: (item: NavigationItem) => void;
}

function NavigationItemRenderer({
  item,
  level = 0,
  isActive = false,
  onNavigate
}: NavigationItemRendererProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { sidebarState, sidebarActions } = useNavigation();
  const { trackNavigation } = useNavigationAnalytics();

  const handleClick = React.useCallback((clickedItem: NavigationItem) => {
    trackNavigation(clickedItem, 'sidebar');
    onNavigate?.(clickedItem);
    
    if (clickedItem.type === 'link') {
      const linkItem = clickedItem as NavigationLink;
      if (linkItem.isExternal) {
        window.open(linkItem.href, linkItem.target || '_blank');
      } else {
        router.push(linkItem.href);
      }
    } else if (clickedItem.type === 'button') {
      const buttonItem = clickedItem as NavigationButton;
      buttonItem.onClick();
    }
  }, [onNavigate, router, trackNavigation]);

  // Don't render if not visible
  if (item.isVisible === false) return null;

  // Separator
  if (item.type === 'separator') {
    return <div className="my-2 border-t border-sidebar-border" />;
  }

  // Group with collapsible items
  if (item.type === 'group') {
    const groupItem = item as NavigationGroup;
    const isExpanded = sidebarState.expandedGroups.includes(item.id) || groupItem.defaultExpanded;

    return (
      <SidebarGroup>
        <Collapsible defaultOpen={groupItem.defaultExpanded}>
          <SidebarGroupLabel asChild>
            <CollapsibleTrigger className="group/collapsible">
              <div className="flex items-center gap-2">
                {groupItem.icon && <groupItem.icon className="h-4 w-4" />}
                <span>{groupItem.title}</span>
                {groupItem.badge && (
                  <Badge variant={groupItem.badge.variant} className="ml-auto">
                    {groupItem.badge.text}
                  </Badge>
                )}
              </div>
              <ChevronDown className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
            </CollapsibleTrigger>
          </SidebarGroupLabel>
          <CollapsibleContent>
            <SidebarGroupContent>
              <SidebarMenuSub>
                {groupItem.items.map((subItem) => (
                  <SidebarMenuSubItem key={subItem.id}>
                    <NavigationItemRenderer
                      item={subItem}
                      level={level + 1}
                      isActive={pathname === (subItem as NavigationLink).href}
                      onNavigate={onNavigate}
                    />
                  </SidebarMenuSubItem>
                ))}
              </SidebarMenuSub>
            </SidebarGroupContent>
          </CollapsibleContent>
        </Collapsible>
      </SidebarGroup>
    );
  }

  // Regular menu item (link or button)
  const ItemIcon = item.icon;
  const isCurrentActive = item.type === 'link' && pathname === (item as NavigationLink).href;

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild={item.type === 'link'}
        isActive={isCurrentActive}
        disabled={item.isDisabled}
        className={cn(
          'group relative',
          level > 0 && 'pl-6',
          item.className
        )}
        onClick={() => item.type === 'button' && handleClick(item)}
      >
        {item.type === 'link' ? (
          <Link 
            href={(item as NavigationLink).href}
            target={(item as NavigationLink).target}
            onClick={() => handleClick(item)}
          >
            {ItemIcon && <ItemIcon className="h-4 w-4" />}
            <span>{item.title}</span>
            {item.badge && (
              <Badge variant={item.badge.variant} className="ml-auto">
                {item.badge.text}
              </Badge>
            )}
          </Link>
        ) : (
          <div className="flex items-center gap-2 w-full">
            {ItemIcon && <ItemIcon className="h-4 w-4" />}
            <span>{item.title}</span>
            {item.badge && (
              <Badge variant={item.badge.variant} className="ml-auto">
                {item.badge.text}
              </Badge>
            )}
          </div>
        )}
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}

// Enhanced App Sidebar Component
export function EnhancedAppSidebar({ 
  config,
  onNavigate,
  className,
  ...props 
}: MainSidebarProps) {
  const { mainSidebar, userRole } = useNavigation();
  const { search } = useNavigationSearch(config?.items || defaultNavigationItems);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [searchResults, setSearchResults] = React.useState<NavigationItem[]>([]);

  // Use provided config or default
  const sidebarConfig = config || {
    ...mainSidebar,
    items: defaultNavigationItems,
  };

  // Handle search
  React.useEffect(() => {
    if (searchQuery.trim()) {
      setSearchResults(search(searchQuery));
    } else {
      setSearchResults([]);
    }
  }, [searchQuery, search]);

  // Filter items based on user permissions
  const visibleItems = React.useMemo(() => {
    return sidebarConfig.items.filter(item => {
      if (item.requiredRoles && !item.requiredRoles.includes(userRole)) {
        return false;
      }
      return item.isVisible !== false;
    });
  }, [sidebarConfig.items, userRole]);

  return (
    <Sidebar
      collapsible="icon"
      className={cn("overflow-hidden", className)}
      {...props}
    >
      {/* Main navigation sidebar */}
      <Sidebar
        collapsible="none"
        className="w-[calc(var(--sidebar-width-icon)+1px)] border-r"
      >
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild className="md:h-8 md:p-0">
                <Link href="/dashboard">
                  <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                    <Cloud className="size-4" />
                  </div>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-medium">
                      {sidebarConfig.branding?.title || 'Omnispace'}
                    </span>
                    <span className="truncate text-xs">
                      {sidebarConfig.branding?.subtitle || 'VM Platform'}
                    </span>
                  </div>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupContent className="px-1.5 md:px-0">
              <SidebarMenu>
                {visibleItems.map((item) => (
                  <NavigationItemRenderer
                    key={item.id}
                    item={item}
                    onNavigate={onNavigate}
                  />
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>

        <SidebarFooter>
          {sidebarConfig.user && (
            <NavUser
              user={{
                ...sidebarConfig.user,
                avatar: sidebarConfig.user.avatar || '/avatars/default.jpg'
              }}
            />
          )}
        </SidebarFooter>
      </Sidebar>

      {/* Secondary sidebar for search and quick actions */}
      <Sidebar collapsible="none" className="hidden flex-1 md:flex">
        <SidebarHeader className="gap-3.5 border-b p-4">
          <div className="flex w-full items-center justify-between">
            <div className="text-foreground text-base font-medium">
              Quick Access
            </div>
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search navigation..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
        </SidebarHeader>

        <SidebarContent>
          <SidebarGroup className="px-0">
            <SidebarGroupContent>
              <AnimatePresence mode="wait">
                {searchQuery.trim() ? (
                  <motion.div
                    key="search-results"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="p-4"
                  >
                    <div className="text-sm font-medium mb-2">
                      Search Results ({searchResults.length})
                    </div>
                    {searchResults.length > 0 ? (
                      <div className="space-y-1">
                        {searchResults.map((result) => (
                          <NavigationItemRenderer
                            key={result.id}
                            item={result}
                            onNavigate={onNavigate}
                          />
                        ))}
                      </div>
                    ) : (
                      <div className="text-sm text-muted-foreground">
                        No results found for "{searchQuery}"
                      </div>
                    )}
                  </motion.div>
                ) : (
                  <motion.div
                    key="quick-actions"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="p-4 space-y-4"
                  >
                    <div>
                      <div className="text-sm font-medium mb-2">Quick Actions</div>
                      <div className="space-y-1">
                        <Button variant="ghost" size="sm" className="w-full justify-start">
                          <Plus className="h-4 w-4 mr-2" />
                          Create Workspace
                        </Button>
                        <Button variant="ghost" size="sm" className="w-full justify-start">
                          <Monitor className="h-4 w-4 mr-2" />
                          Launch VM
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
      </Sidebar>
    </Sidebar>
  );
}
