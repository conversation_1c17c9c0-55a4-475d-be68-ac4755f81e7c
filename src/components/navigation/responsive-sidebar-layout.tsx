'use client';

import React, { Suspense, useMemo } from 'react';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2 } from 'lucide-react';

import { 
  getSidebarForPath, 
  hasSidebar, 
  canAccessSidebar,
  DEFAULT_SIDEBAR_CONFIGS,
  DashboardOverviewSidebar,
  ImagesSidebar,
  EnhancedContainersSidebar,
  ProfileSettingsSidebar,
  SecuritySettingsSidebar,
  BillingSettingsSidebar,
  TeamManagementSidebar,
} from './page-sidebars';
import { PageSidebarProps, UserPermissions } from '@/types/navigation';
import { cn } from '@/lib/utils';

interface ResponsiveSidebarLayoutProps {
  children: React.ReactNode;
  userPermissions?: UserPermissions;
  className?: string;
  sidebarProps?: Partial<PageSidebarProps>;
  onSidebarAction?: (action: any) => void;
}

// Sidebar component mapping for direct imports (better performance)
const SIDEBAR_COMPONENT_MAP = {
  DashboardOverviewSidebar,
  ImagesSidebar,
  EnhancedContainersSidebar,
  ProfileSettingsSidebar,
  SecuritySettingsSidebar,
  BillingSettingsSidebar,
  TeamManagementSidebar,
} as const;

// Loading component for sidebar
function SidebarSkeleton() {
  return (
    <div className="w-80 border-l bg-background">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <div className="h-6 w-32 bg-muted rounded animate-pulse" />
          <div className="h-8 w-16 bg-muted rounded animate-pulse" />
        </div>
        <div className="h-20 bg-muted rounded animate-pulse" />
      </div>
      <div className="p-4 space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <div className="h-4 w-24 bg-muted rounded animate-pulse" />
            <div className="space-y-2">
              {Array.from({ length: 2 }).map((_, j) => (
                <div key={j} className="h-12 bg-muted rounded animate-pulse" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Error boundary for sidebar components
function SidebarErrorBoundary({ 
  children, 
  fallback 
}: { 
  children: React.ReactNode; 
  fallback?: React.ReactNode;
}) {
  const [hasError, setHasError] = React.useState(false);

  React.useEffect(() => {
    setHasError(false);
  }, [children]);

  if (hasError) {
    return (
      <div className="w-80 border-l bg-background p-4">
        <div className="text-center text-muted-foreground">
          <Loader2 className="h-8 w-8 mx-auto mb-2 animate-spin" />
          <p className="text-sm">Loading sidebar...</p>
        </div>
        {fallback}
      </div>
    );
  }

  return (
    <React.Suspense fallback={<SidebarSkeleton />}>
      {children}
    </React.Suspense>
  );
}

export function ResponsiveSidebarLayout({
  children,
  userPermissions,
  className,
  sidebarProps = {},
  onSidebarAction,
}: ResponsiveSidebarLayoutProps) {
  const pathname = usePathname();

  // Determine which sidebar to show
  const sidebarComponentName = useMemo(() => {
    if (!hasSidebar(pathname)) {
      return null;
    }
    
    const sidebarName = getSidebarForPath(pathname);
    
    // Check permissions
    if (sidebarName && userPermissions && !canAccessSidebar(sidebarName, userPermissions)) {
      return null;
    }
    
    return sidebarName;
  }, [pathname, userPermissions]);

  // Get sidebar configuration
  const sidebarConfig = useMemo(() => {
    if (!sidebarComponentName) return null;
    
    const configKey = sidebarComponentName.replace('Sidebar', '').toLowerCase();
    return DEFAULT_SIDEBAR_CONFIGS[configKey as keyof typeof DEFAULT_SIDEBAR_CONFIGS] || null;
  }, [sidebarComponentName]);

  // Render the appropriate sidebar component
  const renderSidebar = () => {
    if (!sidebarComponentName || !sidebarConfig) {
      return null;
    }

    const SidebarComponent = SIDEBAR_COMPONENT_MAP[sidebarComponentName as keyof typeof SIDEBAR_COMPONENT_MAP];
    
    if (!SidebarComponent) {
      console.warn(`Sidebar component ${sidebarComponentName} not found`);
      return null;
    }

    const commonProps = {
      ...sidebarProps,
      userPermissions,
      onQuickAction: onSidebarAction,
      config: sidebarConfig,
    };

    // Add specific props based on sidebar type
    switch (sidebarComponentName) {
      case 'DashboardOverviewSidebar':
        return (
          <SidebarComponent
            {...commonProps}
            onCreateWorkspace={() => onSidebarAction?.({ type: 'create-workspace' })}
            onViewMonitoring={() => onSidebarAction?.({ type: 'view-monitoring' })}
            onManageContainers={() => onSidebarAction?.({ type: 'manage-containers' })}
            onManageImages={() => onSidebarAction?.({ type: 'manage-images' })}
          />
        );
      
      case 'ImagesSidebar':
        return (
          <SidebarComponent
            {...commonProps}
            onPullImage={(imageName: string) => onSidebarAction?.({ type: 'pull-image', imageName })}
            onBuildImage={() => onSidebarAction?.({ type: 'build-image' })}
            onManageRegistry={() => onSidebarAction?.({ type: 'manage-registry' })}
            onViewStorage={() => onSidebarAction?.({ type: 'view-storage' })}
          />
        );
      
      case 'EnhancedContainersSidebar':
        return (
          <SidebarComponent
            {...commonProps}
            onCreateContainer={() => onSidebarAction?.({ type: 'create-container' })}
            onContainerAction={(containerId: string, action: string) => 
              onSidebarAction?.({ type: 'container-action', containerId, action })
            }
          />
        );
      
      case 'ProfileSettingsSidebar':
        return (
          <SidebarComponent
            {...commonProps}
            onProfileUpdate={(updates: any) => onSidebarAction?.({ type: 'profile-update', updates })}
            onPreferencesUpdate={(updates: any) => onSidebarAction?.({ type: 'preferences-update', updates })}
            onAvatarUpload={(file: File) => onSidebarAction?.({ type: 'avatar-upload', file })}
            onPasswordChange={() => onSidebarAction?.({ type: 'password-change' })}
          />
        );
      
      case 'SecuritySettingsSidebar':
        return (
          <SidebarComponent
            {...commonProps}
            onSecurityUpdate={(updates: any) => onSidebarAction?.({ type: 'security-update', updates })}
            onSessionRevoke={(sessionId: string) => onSidebarAction?.({ type: 'session-revoke', sessionId })}
            onRevokeAllSessions={() => onSidebarAction?.({ type: 'revoke-all-sessions' })}
            onEnable2FA={() => onSidebarAction?.({ type: 'enable-2fa' })}
            onDisable2FA={() => onSidebarAction?.({ type: 'disable-2fa' })}
          />
        );
      
      case 'BillingSettingsSidebar':
        return (
          <SidebarComponent
            {...commonProps}
            onUpgradePlan={() => onSidebarAction?.({ type: 'upgrade-plan' })}
            onCancelSubscription={() => onSidebarAction?.({ type: 'cancel-subscription' })}
            onAddPaymentMethod={() => onSidebarAction?.({ type: 'add-payment-method' })}
            onDownloadInvoice={(invoiceId: string) => onSidebarAction?.({ type: 'download-invoice', invoiceId })}
          />
        );
      
      case 'TeamManagementSidebar':
        return (
          <SidebarComponent
            {...commonProps}
            onInviteMember={(email: string, role: any) => onSidebarAction?.({ type: 'invite-member', email, role })}
            onRemoveMember={(memberId: string) => onSidebarAction?.({ type: 'remove-member', memberId })}
            onUpdateMemberRole={(memberId: string, role: any) => 
              onSidebarAction?.({ type: 'update-member-role', memberId, role })
            }
          />
        );
      
      default:
        return <SidebarComponent {...commonProps} />;
    }
  };

  return (
    <div className={cn('flex h-full', className)}>
      {/* Main content */}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>

      {/* Page-specific sidebar */}
      <AnimatePresence mode="wait">
        {sidebarComponentName && (
          <motion.div
            key={sidebarComponentName}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.2, ease: 'easeOut' }}
            className="hidden lg:block"
          >
            <SidebarErrorBoundary>
              {renderSidebar()}
            </SidebarErrorBoundary>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Hook for using sidebar actions in pages
export function useSidebarActions() {
  const handleSidebarAction = React.useCallback((action: any) => {
    console.log('Sidebar action:', action);
    
    // Handle common actions
    switch (action.type) {
      case 'create-workspace':
        // Navigate to workspace creation
        window.location.href = '/dashboard/workspaces/create';
        break;
      
      case 'view-monitoring':
        // Navigate to monitoring page
        window.location.href = '/dashboard/monitoring';
        break;
      
      case 'manage-containers':
        // Navigate to containers page
        window.location.href = '/dashboard/containers';
        break;
      
      case 'manage-images':
        // Navigate to images page
        window.location.href = '/dashboard/images';
        break;
      
      case 'pull-image':
        // Handle image pull
        console.log('Pulling image:', action.imageName);
        break;
      
      case 'build-image':
        // Handle image build
        console.log('Building image');
        break;
      
      case 'profile-update':
        // Handle profile update
        console.log('Updating profile:', action.updates);
        break;
      
      case 'security-update':
        // Handle security settings update
        console.log('Updating security settings:', action.updates);
        break;
      
      case 'upgrade-plan':
        // Handle plan upgrade
        console.log('Upgrading plan');
        break;
      
      case 'invite-member':
        // Handle member invitation
        console.log('Inviting member:', action.email, action.role);
        break;
      
      default:
        console.log('Unhandled sidebar action:', action);
    }
  }, []);

  return { handleSidebarAction };
}

// Export types for external use
export type { ResponsiveSidebarLayoutProps };
