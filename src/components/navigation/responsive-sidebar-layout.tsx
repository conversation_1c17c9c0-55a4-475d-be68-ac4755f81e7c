'use client';

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePathname } from 'next/navigation';
import { X, Menu, PanelLeft, PanelRight } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';

import { useNavigation, useResponsiveSidebar } from '@/contexts/navigation-context';
import { useNavigationUtils } from '@/hooks/useNavigationUtils';
import { EnhancedAppSidebar } from './enhanced-app-sidebar';
import {
  WorkspacesSidebar,
  MonitoringSidebar,
  ContainersSidebar,
  SettingsSidebar,
} from './page-sidebars';
import { ResponsiveConfig, MainSidebarConfig, PageSidebarConfig } from '@/types/navigation';
import { cn } from '@/lib/utils';

// Default responsive configuration
const defaultResponsiveConfig: ResponsiveConfig = {
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1280,
  },
  behavior: {
    mobile: 'overlay',
    tablet: 'collapsed',
    desktop: 'expanded',
  },
  autoCollapse: true,
  persistState: true,
};

interface ResponsiveSidebarLayoutProps {
  children: React.ReactNode;
  mainSidebarConfig?: MainSidebarConfig;
  responsiveConfig?: Partial<ResponsiveConfig>;
  showPageSidebar?: boolean;
  className?: string;
}

// Page sidebar component mapping
const pageSidebarComponents = {
  'dashboard/workspaces': WorkspacesSidebar,
  'dashboard/monitoring': MonitoringSidebar,
  'dashboard/containers': ContainersSidebar,
  'dashboard/settings': SettingsSidebar,
} as const;

export function ResponsiveSidebarLayout({
  children,
  mainSidebarConfig,
  responsiveConfig = {},
  showPageSidebar = true,
  className,
}: ResponsiveSidebarLayoutProps) {
  const pathname = usePathname();
  const { currentPage, pageSidebars, sidebarState, sidebarActions } = useNavigation();
  const { isMobile, shouldShowOverlay } = useResponsiveSidebar();
  
  const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const [isPageSidebarOpen, setIsPageSidebarOpen] = useState(false);

  const config = { ...defaultResponsiveConfig, ...responsiveConfig };

  // Determine screen size
  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width < config.breakpoints.mobile) {
        setScreenSize('mobile');
      } else if (width < config.breakpoints.tablet) {
        setScreenSize('tablet');
      } else {
        setScreenSize('desktop');
      }
    };

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    return () => window.removeEventListener('resize', updateScreenSize);
  }, [config.breakpoints]);

  // Auto-collapse behavior
  useEffect(() => {
    if (config.autoCollapse) {
      const behavior = config.behavior[screenSize];
      if (behavior === 'collapsed' && !sidebarState.isCollapsed) {
        sidebarActions.collapse();
      } else if (behavior === 'expanded' && sidebarState.isCollapsed) {
        sidebarActions.expand();
      }
    }
  }, [screenSize, config.autoCollapse, config.behavior, sidebarState.isCollapsed, sidebarActions]);

  // Close page sidebar on route change (mobile)
  useEffect(() => {
    if (isMobile) {
      setIsPageSidebarOpen(false);
    }
  }, [pathname, isMobile]);

  // Get current page sidebar component
  const getPageSidebarComponent = () => {
    const pageKey = currentPage as keyof typeof pageSidebarComponents;
    return pageSidebarComponents[pageKey];
  };

  const PageSidebarComponent = getPageSidebarComponent();
  const currentPageSidebar = pageSidebars[currentPage];

  // Sidebar animations
  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
    closed: {
      x: '-100%',
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
  };

  const overlayVariants = {
    open: {
      opacity: 1,
      transition: { duration: 0.2 },
    },
    closed: {
      opacity: 0,
      transition: { duration: 0.2 },
    },
  };

  // Mobile sidebar overlay
  const MobileSidebarOverlay = () => (
    <AnimatePresence>
      {shouldShowOverlay && (
        <>
          <motion.div
            initial="closed"
            animate="open"
            exit="closed"
            variants={overlayVariants}
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            onClick={sidebarActions.close}
          />
          <motion.div
            initial="closed"
            animate="open"
            exit="closed"
            variants={sidebarVariants}
            className="fixed left-0 top-0 z-50 h-full w-80 bg-background border-r shadow-lg"
          >
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">Navigation</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={sidebarActions.close}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex-1 overflow-auto">
              <EnhancedAppSidebar config={mainSidebarConfig} />
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );

  // Page sidebar for mobile
  const MobilePageSidebar = () => (
    <Sheet open={isPageSidebarOpen} onOpenChange={setIsPageSidebarOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden"
          disabled={!PageSidebarComponent}
        >
          <PanelRight className="h-4 w-4" />
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="w-80 p-0">
        {PageSidebarComponent && currentPageSidebar && (
          <PageSidebarComponent
            config={currentPageSidebar}
            state={sidebarState}
            actions={sidebarActions}
          />
        )}
      </SheetContent>
    </Sheet>
  );

  // Desktop layout
  if (screenSize === 'desktop') {
    return (
      <div className={cn('min-h-screen bg-background', className)}>
        <SidebarProvider
          style={{
            '--sidebar-width': sidebarState.isCollapsed ? '60px' : '280px',
            '--sidebar-width-icon': '60px',
          } as React.CSSProperties}
        >
          <div className="flex h-screen">
            {/* Main Sidebar */}
            <motion.div
              animate={{
                width: sidebarState.isCollapsed ? 60 : 280,
              }}
              transition={{ duration: 0.2, ease: 'easeInOut' }}
              className="border-r bg-sidebar"
            >
              <EnhancedAppSidebar config={mainSidebarConfig} />
            </motion.div>

            {/* Main Content Area */}
            <div className="flex-1 flex">
              <SidebarInset className="flex-1">
                {children}
              </SidebarInset>

              {/* Page Sidebar */}
              {showPageSidebar && PageSidebarComponent && currentPageSidebar && (
                <motion.div
                  initial={{ width: 0, opacity: 0 }}
                  animate={{ width: 320, opacity: 1 }}
                  exit={{ width: 0, opacity: 0 }}
                  transition={{ duration: 0.3, ease: 'easeInOut' }}
                  className="border-l bg-background"
                >
                  <PageSidebarComponent
                    config={currentPageSidebar}
                    state={sidebarState}
                    actions={sidebarActions}
                  />
                </motion.div>
              )}
            </div>
          </div>
        </SidebarProvider>
      </div>
    );
  }

  // Tablet layout
  if (screenSize === 'tablet') {
    return (
      <div className={cn('min-h-screen bg-background', className)}>
        <SidebarProvider
          style={{
            '--sidebar-width': '60px',
            '--sidebar-width-icon': '60px',
          } as React.CSSProperties}
        >
          <div className="flex h-screen">
            {/* Collapsed Main Sidebar */}
            <div className="w-16 border-r bg-sidebar">
              <EnhancedAppSidebar config={mainSidebarConfig} />
            </div>

            {/* Main Content */}
            <SidebarInset className="flex-1">
              {children}
            </SidebarInset>

            {/* Page Sidebar Toggle */}
            {showPageSidebar && PageSidebarComponent && (
              <div className="fixed bottom-4 right-4 z-40">
                <MobilePageSidebar />
              </div>
            )}
          </div>
        </SidebarProvider>
      </div>
    );
  }

  // Mobile layout
  return (
    <div className={cn('min-h-screen bg-background', className)}>
      {/* Mobile Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur-sm sticky top-0 z-30">
        <Button
          variant="ghost"
          size="icon"
          onClick={sidebarActions.toggle}
        >
          <Menu className="h-4 w-4" />
        </Button>
        
        <h1 className="text-lg font-semibold">Omnispace</h1>
        
        <MobilePageSidebar />
      </div>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Mobile Sidebar Overlay */}
      <MobileSidebarOverlay />
    </div>
  );
}

// Hook for responsive sidebar utilities
export function useResponsiveSidebarLayout() {
  const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const { sidebarState, sidebarActions } = useNavigation();

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setScreenSize('mobile');
      } else if (width < 1024) {
        setScreenSize('tablet');
      } else {
        setScreenSize('desktop');
      }
    };

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);

  const isMobile = screenSize === 'mobile';
  const isTablet = screenSize === 'tablet';
  const isDesktop = screenSize === 'desktop';

  const toggleSidebar = () => {
    if (isMobile) {
      sidebarActions.toggle();
    } else {
      sidebarState.isCollapsed ? sidebarActions.expand() : sidebarActions.collapse();
    }
  };

  return {
    screenSize,
    isMobile,
    isTablet,
    isDesktop,
    toggleSidebar,
    sidebarState,
    sidebarActions,
  };
}
