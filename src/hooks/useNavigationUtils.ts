'use client';

import { useCallback, useMemo } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import {
  NavigationItem,
  NavigationItemFilter,
  NavigationItemTransformer,
  BreadcrumbItem,
  BreadcrumbConfig,
  NavigationUtils,
  NavigationContextValue,
} from '@/types/navigation';
import { useNavigation } from '@/contexts/navigation-context';

/**
 * Hook for navigation utility functions
 */
export function useNavigationUtils(): NavigationUtils {
  const navigation = useNavigation();
  const pathname = usePathname();

  // Filter navigation items based on a predicate
  const filterItems = useCallback((
    items: NavigationItem[],
    filter: NavigationItemFilter
  ): NavigationItem[] => {
    return items.filter(item => filter(item, navigation));
  }, [navigation]);

  // Transform navigation items
  const transformItems = useCallback((
    items: NavigationItem[],
    transformer: NavigationItemTransformer
  ): NavigationItem[] => {
    return items.map(item => transformer(item, navigation));
  }, [navigation]);

  // Find a specific navigation item
  const findItem = useCallback((
    items: NavigationItem[],
    predicate: (item: NavigationItem) => boolean
  ): NavigationItem | undefined => {
    for (const item of items) {
      if (predicate(item)) return item;
      
      // Search in nested items for groups and dropdowns
      if (item.type === 'group' || item.type === 'dropdown') {
        const found = findItem(item.items, predicate);
        if (found) return found;
      }
    }
    return undefined;
  }, []);

  // Flatten nested navigation items
  const flattenItems = useCallback((items: NavigationItem[]): NavigationItem[] => {
    const flattened: NavigationItem[] = [];
    
    for (const item of items) {
      flattened.push(item);
      
      if (item.type === 'group' || item.type === 'dropdown') {
        flattened.push(...flattenItems(item.items));
      }
    }
    
    return flattened;
  }, []);

  // Generate breadcrumbs from current path
  const generateBreadcrumbs = useCallback((
    path?: string,
    config?: Partial<BreadcrumbConfig>
  ): BreadcrumbItem[] => {
    const currentPath = path || pathname;
    const segments = currentPath.split('/').filter(Boolean);
    
    const breadcrumbs: BreadcrumbItem[] = [];
    
    // Add home breadcrumb if enabled
    if (config?.showHome !== false) {
      breadcrumbs.push({
        id: 'home',
        label: 'Dashboard',
        href: config?.homeHref || '/dashboard',
        isClickable: true,
      });
    }
    
    // Generate breadcrumbs for each path segment
    let currentHref = '';
    segments.forEach((segment, index) => {
      currentHref += `/${segment}`;
      const isLast = index === segments.length - 1;
      
      // Convert segment to readable label
      const label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      
      breadcrumbs.push({
        id: segment,
        label,
        href: currentHref,
        isLast,
        isClickable: !isLast,
      });
    });
    
    // Limit breadcrumbs if maxItems is specified
    if (config?.maxItems && breadcrumbs.length > config.maxItems) {
      const start = breadcrumbs.slice(0, 1); // Keep home
      const end = breadcrumbs.slice(-(config.maxItems - 1)); // Keep last items
      return [...start, ...end];
    }
    
    return breadcrumbs;
  }, [pathname]);

  return {
    filterItems,
    transformItems,
    findItem,
    flattenItems,
    generateBreadcrumbs,
  };
}

/**
 * Hook for navigation search functionality
 */
export function useNavigationSearch(items: NavigationItem[]) {
  const { flattenItems } = useNavigationUtils();
  
  const searchableItems = useMemo(() => {
    return flattenItems(items).filter(item => 
      item.type !== 'separator' && item.title
    );
  }, [items, flattenItems]);

  const search = useCallback((query: string): NavigationItem[] => {
    if (!query.trim()) return [];
    
    const lowercaseQuery = query.toLowerCase();
    
    return searchableItems.filter(item => {
      const titleMatch = item.title?.toLowerCase().includes(lowercaseQuery);
      const descriptionMatch = item.description?.toLowerCase().includes(lowercaseQuery);
      
      return titleMatch || descriptionMatch;
    });
  }, [searchableItems]);

  return { search, searchableItems };
}

/**
 * Hook for navigation keyboard shortcuts
 */
export function useNavigationKeyboard() {
  const { sidebarActions } = useNavigation();
  const router = useRouter();

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Check for modifier keys
    const isCtrl = event.ctrlKey || event.metaKey;
    const isShift = event.shiftKey;
    
    // Sidebar toggle (Ctrl/Cmd + B)
    if (isCtrl && event.key === 'b') {
      event.preventDefault();
      sidebarActions.toggle();
      return;
    }
    
    // Quick navigation shortcuts
    if (isCtrl && isShift) {
      switch (event.key) {
        case 'D':
          event.preventDefault();
          router.push('/dashboard');
          break;
        case 'W':
          event.preventDefault();
          router.push('/dashboard/workspaces');
          break;
        case 'M':
          event.preventDefault();
          router.push('/dashboard/monitoring');
          break;
        case 'C':
          event.preventDefault();
          router.push('/dashboard/containers');
          break;
        case 'I':
          event.preventDefault();
          router.push('/dashboard/images');
          break;
        case 'S':
          event.preventDefault();
          router.push('/dashboard/settings');
          break;
      }
    }
  }, [sidebarActions, router]);

  return { handleKeyDown };
}

/**
 * Hook for navigation analytics and tracking
 */
export function useNavigationAnalytics() {
  const trackNavigation = useCallback((item: NavigationItem, context?: string) => {
    // TODO: Implement analytics tracking
    console.log('Navigation tracked:', {
      item: item.title,
      type: item.type,
      context,
      timestamp: new Date().toISOString(),
    });
  }, []);

  const trackSidebarAction = useCallback((action: string, data?: any) => {
    // TODO: Implement sidebar action tracking
    console.log('Sidebar action tracked:', {
      action,
      data,
      timestamp: new Date().toISOString(),
    });
  }, []);

  return { trackNavigation, trackSidebarAction };
}

/**
 * Hook for navigation state persistence
 */
export function useNavigationPersistence() {
  const { sidebarState, sidebarActions } = useNavigation();

  const saveState = useCallback(() => {
    try {
      localStorage.setItem('navigation-state', JSON.stringify(sidebarState));
    } catch (error) {
      console.warn('Failed to save navigation state:', error);
    }
  }, [sidebarState]);

  const loadState = useCallback(() => {
    try {
      const saved = localStorage.getItem('navigation-state');
      if (saved) {
        const state = JSON.parse(saved);
        // Apply saved state through actions
        if (state.isCollapsed) sidebarActions.collapse();
        if (!state.isOpen) sidebarActions.close();
        if (state.activeItem) sidebarActions.setActiveItem(state.activeItem);
      }
    } catch (error) {
      console.warn('Failed to load navigation state:', error);
    }
  }, [sidebarActions]);

  const clearState = useCallback(() => {
    try {
      localStorage.removeItem('navigation-state');
    } catch (error) {
      console.warn('Failed to clear navigation state:', error);
    }
  }, []);

  return { saveState, loadState, clearState };
}

/**
 * Hook for navigation accessibility features
 */
export function useNavigationAccessibility() {
  const announceNavigation = useCallback((message: string) => {
    // Create or update ARIA live region for announcements
    let liveRegion = document.getElementById('navigation-announcements');
    
    if (!liveRegion) {
      liveRegion = document.createElement('div');
      liveRegion.id = 'navigation-announcements';
      liveRegion.setAttribute('aria-live', 'polite');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'sr-only';
      document.body.appendChild(liveRegion);
    }
    
    liveRegion.textContent = message;
    
    // Clear the message after a delay
    setTimeout(() => {
      if (liveRegion) liveRegion.textContent = '';
    }, 1000);
  }, []);

  const focusFirstItem = useCallback(() => {
    const firstFocusable = document.querySelector(
      '[data-sidebar] [role="menuitem"], [data-sidebar] button, [data-sidebar] a'
    ) as HTMLElement;
    
    if (firstFocusable) {
      firstFocusable.focus();
    }
  }, []);

  const skipToContent = useCallback(() => {
    const mainContent = document.querySelector('main') as HTMLElement;
    if (mainContent) {
      mainContent.focus();
      mainContent.scrollIntoView();
    }
  }, []);

  return {
    announceNavigation,
    focusFirstItem,
    skipToContent,
  };
}
