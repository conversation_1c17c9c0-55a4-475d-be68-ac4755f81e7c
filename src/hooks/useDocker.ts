import { useState, useEffect, useCallback } from 'react';
import { ContainerInfo, CreateContainerOptions } from '@/types/docker';
import { dockerFallbackService } from '@/services/client/docker-fallback';
import { useAuth } from '@/contexts/auth-context';

interface UseDockerReturn {
  containers: ContainerInfo[];
  loading: boolean;
  error: string | null;
  systemInfo: any;
  connected: boolean;
  refreshContainers: () => Promise<void>;
  createContainer: (options: CreateContainerOptions) => Promise<string>;
  startContainer: (id: string) => Promise<void>;
  stopContainer: (id: string, timeout?: number) => Promise<void>;
  restartContainer: (id: string) => Promise<void>;
  removeContainer: (id: string, force?: boolean) => Promise<void>;
  getContainerLogs: (id: string, tail?: number) => Promise<string>;
  getContainerStats: (id: string) => Promise<any>;
  execInContainer: (id: string, cmd: string[]) => Promise<string>;
  pullImage: (imageName: string) => Promise<void>;
  getSystemInfo: () => Promise<void>;
}

export function useDocker(): UseDockerReturn {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [containers, setContainers] = useState<ContainerInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [systemInfo, setSystemInfo] = useState<any>(null);
  const [connected, setConnected] = useState(false);

  const refreshContainers = useCallback(async () => {
    if (!isAuthenticated) {
      setContainers([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const containers = await dockerFallbackService.listContainers();
      setContainers(containers);
    } catch (error) {
      console.error('Error fetching containers:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
      setContainers([]);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  const getSystemInfo = useCallback(async () => {
    if (!isAuthenticated) {
      setSystemInfo(null);
      setConnected(false);
      return;
    }

    try {
      const systemInfo = await dockerFallbackService.getSystemInfo();
      setSystemInfo(systemInfo);
      setConnected(systemInfo.connected !== false);
    } catch (error) {
      setConnected(false);
      setSystemInfo(null);
    }
  }, [isAuthenticated]);

  const createContainer = useCallback(async (options: CreateContainerOptions): Promise<string> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    const containerId = await dockerFallbackService.createContainer(options);
    await refreshContainers();
    return containerId;
  }, [refreshContainers, isAuthenticated]);

  const startContainer = useCallback(async (id: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    await dockerFallbackService.startContainer(id);
    await refreshContainers();
  }, [refreshContainers, isAuthenticated]);

  const stopContainer = useCallback(async (id: string, timeout = 10): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    await dockerFallbackService.stopContainer(id, timeout);
    await refreshContainers();
  }, [refreshContainers, isAuthenticated]);

  const restartContainer = useCallback(async (id: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    await dockerFallbackService.restartContainer(id);
    await refreshContainers();
  }, [refreshContainers, isAuthenticated]);

  const removeContainer = useCallback(async (id: string, force = false): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    await dockerFallbackService.removeContainer(id, force);
    await refreshContainers();
  }, [refreshContainers, isAuthenticated]);

  const getContainerLogs = useCallback(async (id: string, tail = 100): Promise<string> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    return await dockerFallbackService.getContainerLogs(id, tail);
  }, [isAuthenticated]);

  const getContainerStats = useCallback(async (id: string): Promise<any> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    return await dockerFallbackService.getContainerStats(id);
  }, [isAuthenticated]);

  const execInContainer = useCallback(async (id: string, cmd: string[]): Promise<string> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    return await dockerFallbackService.execInContainer(id, cmd);
  }, [isAuthenticated]);

  const pullImage = useCallback(async (imageName: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    await dockerFallbackService.pullImage(imageName);
  }, [isAuthenticated]);

  // Initialize data on authentication
  useEffect(() => {
    if (!authLoading) {
      const initializeData = async () => {
        await getSystemInfo();
        await refreshContainers();
      };
      
      initializeData();
    }
  }, [authLoading, isAuthenticated, getSystemInfo, refreshContainers]);

  return {
    containers,
    loading,
    error,
    systemInfo,
    connected,
    refreshContainers,
    createContainer,
    startContainer,
    stopContainer,
    restartContainer,
    removeContainer,
    getContainerLogs,
    getContainerStats,
    execInContainer,
    pullImage,
    getSystemInfo,
  };
}

// Utility hook for managing a specific container
export function useContainer(containerId: string) {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [container, setContainer] = useState<ContainerInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshContainer = useCallback(async () => {
    if (!containerId || !isAuthenticated) {
      setContainer(null);
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Get session cookie
      const sessionCookie = document.cookie
        .split('; ')
        .find(row => row.startsWith('appwrite-session='))
        ?.split('=')[1];

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (sessionCookie) {
        headers['x-appwrite-session'] = sessionCookie;
      }
      
      const response = await fetch(`/api/containers/${containerId}`, {
        method: 'GET',
        headers,
        credentials: 'include'
      });
      
      const data = await response.json();
      
      if (!response.ok || !data.success) {
        throw new Error(data.message || 'Failed to fetch container');
      }
      
      setContainer(data.data);
    } catch (error) {
      console.error('Error fetching container:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
      setContainer(null);
    } finally {
      setLoading(false);
    }
  }, [containerId, isAuthenticated]);

  useEffect(() => {
    if (!authLoading) {
      refreshContainer();
    }
  }, [authLoading, refreshContainer]);

  return {
    container,
    loading,
    error,
    refreshContainer,
  };
}
