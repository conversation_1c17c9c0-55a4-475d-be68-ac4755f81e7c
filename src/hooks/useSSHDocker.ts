import { useState, useEffect, useCallback } from 'react';
import { 
  SSHConnectionConfig, 
  SSHConnectionStatus, 
  RemoteContainerInfo, 
  RemoteDockerImage,
  RemoteDockerCommand,
  RemoteDockerCommandResult
} from '@/types/ssh-docker';
import { sshDockerAPIService } from '@/services/client/ssh-docker-api';
import { useAuth } from '@/contexts/auth-context';

interface UseSSHDockerReturn {
  connections: SSHConnectionStatus[];
  loading: boolean;
  error: string | null;
  
  // Connection management
  testConnection: (config: SSHConnectionConfig) => Promise<boolean>;
  connect: (config: SSHConnectionConfig) => Promise<void>;
  disconnect: (connectionId: string) => Promise<void>;
  refreshConnections: () => Promise<void>;
  
  // Remote operations
  listRemoteContainers: (connectionId: string) => Promise<RemoteContainerInfo[]>;
  listRemoteImages: (connectionId: string) => Promise<RemoteDockerImage[]>;
  startRemoteContainer: (connectionId: string, containerId: string) => Promise<void>;
  stopRemoteContainer: (connectionId: string, containerId: string) => Promise<void>;
  removeRemoteContainer: (connectionId: string, containerId: string) => Promise<void>;
  getRemoteContainerLogs: (connectionId: string, containerId: string, tail?: number) => Promise<string>;
  execRemoteCommand: (connectionId: string, command: RemoteDockerCommand) => Promise<RemoteDockerCommandResult>;
  pullRemoteImage: (connectionId: string, imageName: string) => Promise<void>;
  
  // Config management
  saveConnectionConfig: (config: SSHConnectionConfig) => Promise<void>;
  getSavedConnectionConfigs: () => Promise<SSHConnectionConfig[]>;
  deleteConnectionConfig: (connectionId: string) => Promise<void>;
}

export function useSSHDocker(): UseSSHDockerReturn {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [connections, setConnections] = useState<SSHConnectionStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshConnections = useCallback(async () => {
    if (!isAuthenticated) {
      setConnections([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const connectionsData = await sshDockerAPIService.listConnections();
      setConnections(connectionsData);
    } catch (error) {
      console.error('Error fetching SSH connections:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
      setConnections([]);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  const testConnection = useCallback(async (config: SSHConnectionConfig): Promise<boolean> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    return await sshDockerAPIService.testConnection(config);
  }, [isAuthenticated]);

  const connect = useCallback(async (config: SSHConnectionConfig): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    await sshDockerAPIService.connect(config);
    await refreshConnections();
  }, [isAuthenticated, refreshConnections]);

  const disconnect = useCallback(async (connectionId: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    await sshDockerAPIService.disconnect(connectionId);
    await refreshConnections();
  }, [isAuthenticated, refreshConnections]);

  const listRemoteContainers = useCallback(async (connectionId: string): Promise<RemoteContainerInfo[]> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    return await sshDockerAPIService.listRemoteContainers(connectionId);
  }, [isAuthenticated]);

  const listRemoteImages = useCallback(async (connectionId: string): Promise<RemoteDockerImage[]> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    return await sshDockerAPIService.listRemoteImages(connectionId);
  }, [isAuthenticated]);

  const startRemoteContainer = useCallback(async (connectionId: string, containerId: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    await sshDockerAPIService.startRemoteContainer(connectionId, containerId);
  }, [isAuthenticated]);

  const stopRemoteContainer = useCallback(async (connectionId: string, containerId: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    await sshDockerAPIService.stopRemoteContainer(connectionId, containerId);
  }, [isAuthenticated]);

  const removeRemoteContainer = useCallback(async (connectionId: string, containerId: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    await sshDockerAPIService.removeRemoteContainer(connectionId, containerId);
  }, [isAuthenticated]);

  const getRemoteContainerLogs = useCallback(async (connectionId: string, containerId: string, tail = 100): Promise<string> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    return await sshDockerAPIService.getRemoteContainerLogs(connectionId, containerId, tail);
  }, [isAuthenticated]);

  const execRemoteCommand = useCallback(async (connectionId: string, command: RemoteDockerCommand): Promise<RemoteDockerCommandResult> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    return await sshDockerAPIService.execRemoteCommand(connectionId, command);
  }, [isAuthenticated]);

  const pullRemoteImage = useCallback(async (connectionId: string, imageName: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    await sshDockerAPIService.pullRemoteImage(connectionId, imageName);
  }, [isAuthenticated]);

  const saveConnectionConfig = useCallback(async (config: SSHConnectionConfig): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    await sshDockerAPIService.saveConnectionConfig(config);
  }, [isAuthenticated]);

  const getSavedConnectionConfigs = useCallback(async (): Promise<SSHConnectionConfig[]> => {
    if (!isAuthenticated) {
      return [];
    }
    
    return await sshDockerAPIService.getSavedConnectionConfigs();
  }, [isAuthenticated]);

  const deleteConnectionConfig = useCallback(async (connectionId: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    await sshDockerAPIService.deleteConnectionConfig(connectionId);
  }, [isAuthenticated]);

  // Initialize data on authentication
  useEffect(() => {
    if (!authLoading) {
      refreshConnections();
    }
  }, [authLoading, refreshConnections]);

  return {
    connections,
    loading,
    error,
    testConnection,
    connect,
    disconnect,
    refreshConnections,
    listRemoteContainers,
    listRemoteImages,
    startRemoteContainer,
    stopRemoteContainer,
    removeRemoteContainer,
    getRemoteContainerLogs,
    execRemoteCommand,
    pullRemoteImage,
    saveConnectionConfig,
    getSavedConnectionConfigs,
    deleteConnectionConfig,
  };
}

// Hook for managing a specific SSH connection
export function useSSHConnection(connectionId: string) {
  const { isAuthenticated } = useAuth();
  const [status, setStatus] = useState<SSHConnectionStatus | null>(null);
  const [containers, setContainers] = useState<RemoteContainerInfo[]>([]);
  const [images, setImages] = useState<RemoteDockerImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshStatus = useCallback(async () => {
    if (!connectionId || !isAuthenticated) {
      setStatus(null);
      setLoading(false);
      return;
    }

    try {
      const statusData = await sshDockerAPIService.getConnectionStatus(connectionId);
      setStatus(statusData);
    } catch (error) {
      console.error('Error fetching connection status:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  }, [connectionId, isAuthenticated]);

  const refreshContainers = useCallback(async () => {
    if (!connectionId || !isAuthenticated) {
      setContainers([]);
      return;
    }

    try {
      const containersData = await sshDockerAPIService.listRemoteContainers(connectionId);
      setContainers(containersData);
    } catch (error) {
      console.error('Error fetching remote containers:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  }, [connectionId, isAuthenticated]);

  const refreshImages = useCallback(async () => {
    if (!connectionId || !isAuthenticated) {
      setImages([]);
      return;
    }

    try {
      const imagesData = await sshDockerAPIService.listRemoteImages(connectionId);
      setImages(imagesData);
    } catch (error) {
      console.error('Error fetching remote images:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  }, [connectionId, isAuthenticated]);

  const refreshAll = useCallback(async () => {
    setLoading(true);
    await Promise.all([
      refreshStatus(),
      refreshContainers(),
      refreshImages(),
    ]);
    setLoading(false);
  }, [refreshStatus, refreshContainers, refreshImages]);

  useEffect(() => {
    refreshAll();
  }, [refreshAll]);

  return {
    status,
    containers,
    images,
    loading,
    error,
    refreshStatus,
    refreshContainers,
    refreshImages,
    refreshAll,
  };
}