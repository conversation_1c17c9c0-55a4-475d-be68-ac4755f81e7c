// This hook is deprecated - use useAuth from auth-context instead
// Re-export for compatibility with existing components
export { useAuth } from '@/contexts/auth-context';

// Deprecated: Use useAuth from auth-context instead
export const useAppwriteAuth = () => {
  console.warn('useAppwriteAuth is deprecated. Use useAuth from @/contexts/auth-context instead.');
  const { useAuth } = require('@/contexts/auth-context');
  return useAuth();
};


