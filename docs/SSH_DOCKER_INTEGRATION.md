# SSH Docker Integration

## Overview

The SSH Docker integration allows you to connect to remote servers via SSH and manage Docker containers, images, and other Docker resources on those remote systems. This enables centralized management of multiple Docker hosts from a single interface.

## Architecture

### Core Components

1. **SSH Connection Service** (`src/services/server/ssh-docker.ts`)
   - Manages SSH connections using the `ssh2` library
   - Handles connection pooling and lifecycle management
   - Executes remote Docker commands via SSH

2. **Client API Service** (`src/services/client/ssh-docker-api.ts`)
   - Provides client-side interface for SSH Docker operations
   - Handles API communication and error management
   - Manages authentication headers

3. **React Hooks** (`src/hooks/useSSHDocker.ts`)
   - `useSSHDocker()` - Main hook for SSH Docker operations
   - `useSSHConnection()` - Hook for managing specific connections
   - Reactive state management for connections and containers

4. **UI Components**
   - `SSHConnectionManager` - Connection configuration and management
   - `RemoteContainersView` - Remote container management interface
   - `EnhancedContainersSidebar` - Unified local/remote container view

### API Endpoints

```
/api/ssh-docker/
├── test                          # POST - Test SSH connection
├── connect                       # POST - Establish SSH connection
├── disconnect/[connectionId]     # POST - Disconnect SSH connection
├── status/[connectionId]         # GET  - Get connection status
├── connections                   # GET  - List all connections
├── config                        # GET/POST/DELETE - Manage saved configs
├── config/[connectionId]         # GET/DELETE - Specific config management
└── [connectionId]/
    ├── containers               # GET - List remote containers
    ├── images                   # GET - List remote images
    ├── exec                     # POST - Execute remote command
    ├── containers/[containerId]/
    │   ├── start               # POST - Start container
    │   ├── stop                # POST - Stop container
    │   ├── logs                # GET - Get container logs
    │   └── route               # DELETE - Remove container
    └── images/
        └── pull                # POST - Pull image
```

## Features

### SSH Connection Management

- **Multiple Authentication Methods**:
  - Password authentication
  - Private key authentication (with optional passphrase)
  - SSH agent authentication

- **Connection Lifecycle**:
  - Test connections before establishing
  - Persistent connection management
  - Automatic reconnection on failure
  - Connection health monitoring

- **Security Features**:
  - Encrypted SSH connections
  - Key-based authentication
  - Session management
  - Connection timeout handling

### Remote Docker Operations

- **Container Management**:
  - List all containers (running and stopped)
  - Start/stop/restart containers
  - Remove containers
  - View real-time logs
  - Execute commands in containers

- **Image Management**:
  - List available images
  - Pull new images
  - Image metadata and sizing

- **System Information**:
  - Docker version detection
  - System resource monitoring
  - Connection status tracking

## Usage

### Basic Connection Setup

```typescript
import { useSSHDocker } from '@/hooks/useSSHDocker';

function MyComponent() {
  const { testConnection, connect, connections } = useSSHDocker();

  const handleConnect = async () => {
    const config = {
      id: 'my-server',
      name: 'Production Server',
      host: '*************',
      port: 22,
      username: 'admin',
      authentication: {
        type: 'privateKey',
        privateKey: '/path/to/private/key'
      }
    };

    // Test connection first
    const isValid = await testConnection(config);
    if (isValid) {
      await connect(config);
    }
  };

  return (
    <div>
      {connections.map(conn => (
        <div key={conn.id}>
          {conn.connected ? 'Connected' : 'Disconnected'}: {conn.id}
        </div>
      ))}
    </div>
  );
}
```

### Remote Container Management

```typescript
import { useSSHConnection } from '@/hooks/useSSHDocker';

function RemoteContainers({ connectionId }: { connectionId: string }) {
  const { containers, status, refreshContainers } = useSSHConnection(connectionId);

  if (!status?.connected) {
    return <div>Not connected</div>;
  }

  return (
    <div>
      <h3>Remote Containers ({containers.length})</h3>
      {containers.map(container => (
        <div key={container.id}>
          {container.name} - {container.status}
        </div>
      ))}
    </div>
  );
}
```

## Configuration Templates

The system includes pre-configured templates for common server types:

- **Ubuntu Server** - Standard Ubuntu with Docker
- **AWS EC2** - EC2 instances with key authentication
- **DigitalOcean** - Droplets with SSH keys
- **Password Auth** - Simple password authentication
- **SSH Agent** - Agent-based authentication

## Security Considerations

### Data Protection

- **No Password Storage**: Passwords are never stored on disk or in client storage
- **Key Path Storage**: Only private key file paths are stored, not key contents
- **Session Management**: SSH sessions are properly managed and cleaned up
- **Connection Encryption**: All data transmitted over encrypted SSH connections

### Best Practices

1. **Use Key-Based Authentication**: More secure than passwords
2. **Limit SSH Access**: Use dedicated SSH keys for Docker management
3. **Monitor Connections**: Regular connection health checks
4. **Secure Key Storage**: Protect private keys with appropriate file permissions
5. **Network Security**: Use VPNs or secure networks for SSH connections

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Check network connectivity
   - Verify SSH service is running
   - Confirm correct port (default: 22)

2. **Authentication Failed**
   - Verify username and credentials
   - Check SSH key permissions (600 for private keys)
   - Ensure SSH agent is running (for agent auth)

3. **Docker Not Available**
   - Confirm Docker is installed on remote server
   - Check Docker daemon is running
   - Verify user has Docker permissions

4. **Permission Denied**
   - Check SSH user has Docker group membership
   - Verify sudo access if required
   - Confirm file system permissions

### Debug Information

Enable debug logging by setting the `DEBUG_SSH` environment variable:

```bash
DEBUG_SSH=true npm run dev
```

This will provide detailed SSH connection and command execution logs.

## Performance Considerations

- **Connection Pooling**: Reuse SSH connections for multiple operations
- **Command Batching**: Group related Docker commands when possible
- **Timeout Management**: Appropriate timeouts for different operations
- **Resource Cleanup**: Proper cleanup of SSH connections and streams

## Future Enhancements

- **Multi-Host Operations**: Bulk operations across multiple servers
- **Container Orchestration**: Docker Compose and Swarm support
- **File Transfer**: SFTP integration for file management
- **Real-time Monitoring**: WebSocket-based real-time updates
- **Backup/Restore**: Container and volume backup operations
- **Log Streaming**: Real-time log streaming with WebSockets
- **Performance Metrics**: Detailed resource usage monitoring