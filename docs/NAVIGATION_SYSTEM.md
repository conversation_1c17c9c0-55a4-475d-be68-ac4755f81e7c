# Omnispace Navigation System

A comprehensive, accessible, and responsive navigation system for the Omnispace platform with role-based access control, glassmorphism effects, and Framer Motion animations.

## Features

- **🎯 Role-Based Access Control**: Show/hide navigation items based on user roles and permissions
- **📱 Responsive Design**: Adaptive layouts for mobile, tablet, and desktop
- **♿ Accessibility**: Full keyboard navigation, screen reader support, and ARIA compliance
- **✨ Glassmorphism Effects**: Modern glass-like visual effects with backdrop blur
- **🎬 Smooth Animations**: Framer Motion powered transitions and micro-interactions
- **🔍 Search & Filtering**: Built-in search functionality for navigation items
- **📍 Breadcrumb Navigation**: Automatic breadcrumb generation from routes
- **💾 State Persistence**: Sidebar state saved across sessions
- **🎨 Theming**: Dark/light mode support with customizable themes

## Architecture

### Core Components

```
src/components/navigation/
├── enhanced-app-sidebar.tsx          # Main dashboard sidebar
├── responsive-sidebar-layout.tsx     # Responsive layout wrapper
├── animated-sidebar-wrapper.tsx      # Animation and glassmorphism effects
├── accessible-sidebar.tsx            # Accessibility features
├── role-based-navigation.tsx         # Role-based access control
└── page-sidebars/                    # Page-specific sidebars
    ├── workspaces-sidebar.tsx
    ├── monitoring-sidebar.tsx
    ├── containers-sidebar.tsx
    └── settings-sidebar.tsx
```

### Context & Hooks

```
src/contexts/navigation-context.tsx    # Navigation state management
src/hooks/useNavigationUtils.ts        # Navigation utility hooks
```

### Types & Utilities

```
src/types/navigation.ts               # TypeScript type definitions
src/lib/navigation-utils.ts           # Utility functions and helpers
```

## Quick Start

### 1. Basic Setup

```tsx
import { NavigationProvider } from '@/components/navigation';

function App() {
  return (
    <NavigationProvider>
      {/* Your app content */}
    </NavigationProvider>
  );
}
```

### 2. Dashboard Layout

```tsx
import { 
  ResponsiveSidebarLayout,
  RoleBasedNavigation,
  AccessibleSidebar 
} from '@/components/navigation';

function DashboardLayout({ children }) {
  return (
    <NavigationProvider>
      <RoleBasedNavigation items={navigationItems}>
        <AccessibleSidebar>
          <ResponsiveSidebarLayout>
            {children}
          </ResponsiveSidebarLayout>
        </AccessibleSidebar>
      </RoleBasedNavigation>
    </NavigationProvider>
  );
}
```

### 3. Navigation Items Configuration

```tsx
import { NavigationItem } from '@/types/navigation';

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    type: 'link',
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
    description: 'Main dashboard overview',
  },
  {
    id: 'workspaces',
    type: 'group',
    title: 'Workspaces',
    icon: Monitor,
    collapsible: true,
    items: [
      {
        id: 'all-workspaces',
        type: 'link',
        title: 'All Workspaces',
        href: '/dashboard/workspaces',
      },
      {
        id: 'create-workspace',
        type: 'link',
        title: 'Create Workspace',
        href: '/dashboard/workspaces/create',
        requiredPermissions: [{ action: 'write', resource: 'workspaces' }],
      },
    ],
  },
];
```

## Role-Based Access Control

### Defining Roles and Permissions

```tsx
const rolePermissions: RolePermissions[] = [
  {
    role: 'admin',
    permissions: [
      { action: 'read', resource: '*' },
      { action: 'write', resource: '*' },
      { action: 'delete', resource: '*' },
    ],
  },
  {
    role: 'user',
    permissions: [
      { action: 'read', resource: 'dashboard' },
      { action: 'read', resource: 'workspaces' },
      { action: 'write', resource: 'workspaces', conditions: { owner: true } },
    ],
  },
];
```

### Using Permission Guards

```tsx
import { PermissionGuard } from '@/components/navigation';

function AdminOnlySection() {
  return (
    <PermissionGuard requiredRoles={['admin']}>
      <AdminPanel />
    </PermissionGuard>
  );
}
```

## Page-Specific Sidebars

### Creating Custom Page Sidebars

```tsx
import { PageSidebarProps } from '@/types/navigation';

interface CustomSidebarProps extends Omit<PageSidebarProps, 'config'> {
  // Custom props
}

export function CustomSidebar({ onQuickAction, ...props }: CustomSidebarProps) {
  return (
    <Sidebar className="w-80 border-l" {...props}>
      <SidebarHeader>
        <h2>Custom Sidebar</h2>
      </SidebarHeader>
      <SidebarContent>
        {/* Sidebar content */}
      </SidebarContent>
    </Sidebar>
  );
}
```

### Registering Page Sidebars

```tsx
// In responsive-sidebar-layout.tsx
const pageSidebarComponents = {
  'dashboard/custom': CustomSidebar,
  // ... other sidebars
};
```

## Accessibility Features

### Keyboard Navigation

- `Ctrl/Cmd + B`: Toggle sidebar
- `Ctrl/Cmd + Shift + D`: Go to dashboard
- `Arrow Up/Down`: Navigate between items
- `Enter/Space`: Activate item
- `Escape`: Close sidebar
- `Tab/Shift+Tab`: Focus management

### Screen Reader Support

- ARIA labels and descriptions
- Live region announcements
- Semantic HTML structure
- Focus management

### Accessibility Controls

```tsx
import { AccessibleSidebar } from '@/components/navigation';

<AccessibleSidebar
  ariaLabel="Main navigation"
  ariaDescription="Navigate through dashboard sections"
  keyboardConfig={{
    enabled: true,
    shortcuts: {
      'Ctrl+B': 'Toggle sidebar',
      // ... custom shortcuts
    },
  }}
>
  {/* Navigation content */}
</AccessibleSidebar>
```

## Animations and Theming

### Glassmorphism Configuration

```tsx
const themeConfig = {
  glassmorphism: {
    enabled: true,
    opacity: 0.8,
    blur: 12,
  },
  colors: {
    background: 'hsl(var(--sidebar))',
    foreground: 'hsl(var(--sidebar-foreground))',
    // ... other colors
  },
};
```

### Animation Configuration

```tsx
const animationConfig = {
  enabled: true,
  duration: 0.3,
  easing: 'easeInOut',
  stagger: 0.05,
  hover: {
    scale: 1.02,
    duration: 0.2,
  },
};
```

## Responsive Behavior

### Breakpoints

- **Mobile**: < 768px (overlay sidebar)
- **Tablet**: 768px - 1024px (collapsed sidebar)
- **Desktop**: > 1024px (expanded sidebar)

### Configuration

```tsx
const responsiveConfig = {
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1280,
  },
  behavior: {
    mobile: 'overlay',
    tablet: 'collapsed',
    desktop: 'expanded',
  },
  autoCollapse: true,
  persistState: true,
};
```

## State Management

### Using Navigation Context

```tsx
import { useNavigation } from '@/components/navigation';

function NavigationComponent() {
  const {
    sidebarState,
    sidebarActions,
    currentPage,
    userRole,
    checkPermission,
  } = useNavigation();

  return (
    <div>
      <button onClick={sidebarActions.toggle}>
        Toggle Sidebar
      </button>
      <p>Current page: {currentPage}</p>
      <p>User role: {userRole}</p>
    </div>
  );
}
```

### State Persistence

```tsx
import { NavigationStateManager } from '@/lib/navigation-utils';

// Save state
NavigationStateManager.saveSidebarState(sidebarState);

// Load state
const savedState = NavigationStateManager.loadSidebarState();

// Clear state
NavigationStateManager.clearSidebarState();
```

## Breadcrumb Navigation

### Automatic Generation

```tsx
import { BreadcrumbGenerator } from '@/lib/navigation-utils';

const breadcrumbs = BreadcrumbGenerator.generateFromPath('/dashboard/workspaces/create');
```

### Custom Breadcrumbs

```tsx
const customBreadcrumbs: BreadcrumbItem[] = [
  {
    id: 'home',
    label: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    id: 'workspaces',
    label: 'Workspaces',
    href: '/dashboard/workspaces',
    icon: Monitor,
  },
  {
    id: 'create',
    label: 'Create',
    isLast: true,
  },
];
```

## Best Practices

1. **Performance**: Use React.memo for navigation items that don't change frequently
2. **Accessibility**: Always provide meaningful labels and descriptions
3. **Permissions**: Check permissions at both component and API levels
4. **State**: Keep sidebar state minimal and focused
5. **Testing**: Test keyboard navigation and screen reader compatibility
6. **Responsive**: Design mobile-first, enhance for larger screens

## Troubleshooting

### Common Issues

1. **Sidebar not showing**: Check NavigationProvider wrapper
2. **Permissions not working**: Verify role configuration and user context
3. **Animations not smooth**: Check for CSS conflicts or reduced motion settings
4. **Mobile layout issues**: Verify responsive configuration and breakpoints

### Debug Mode

```tsx
// Enable debug logging
localStorage.setItem('navigation-debug', 'true');
```

## Contributing

When adding new navigation features:

1. Update TypeScript types in `src/types/navigation.ts`
2. Add utility functions to `src/lib/navigation-utils.ts`
3. Create tests for new components
4. Update this documentation
5. Ensure accessibility compliance
